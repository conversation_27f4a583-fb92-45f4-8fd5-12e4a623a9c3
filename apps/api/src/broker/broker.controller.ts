import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  Logger,
  UseGuards,
  ParseIntPipe,
  UnauthorizedException,
} from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import { AuthGuard, RolesGuard, Roles } from '@app/auth';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { BrokerService, BrokerAuthService } from '@app/broker';
import { PublicBroker } from '@app/broker/broker.schema';
import { LoginUrlResponse, AccessTokenResponse, SessionValidationResponse } from '@app/broker/broker-auth.service';
import {
  CreateBrokerDto,
  UpdateBrokerDto,
  BrokerSearchDto,
  GenerateLoginUrlDto,
  GenerateAccessTokenDto,
  ValidateSessionDto,
} from './broker.dto';

// ==================== CONTROLLER ====================

/**
 * Broker Controller for API Gateway
 *
 * Provides REST API endpoints for broker credential management operations.
 * Handles broker configuration CRUD operations with proper authentication,
 * authorization, validation, and error handling.
 *
 * Features:
 * - Broker credential management (create, read, update, delete)
 * - Zerodha Kite Connect authentication services
 * - User-specific broker configurations
 * - Comprehensive search and filtering
 * - Pagination support for large datasets
 * - Role-based access control
 * - Input validation with Zod schemas
 * - Structured logging and error handling
 */
@Controller('brokers')
@UseGuards(AuthGuard, RolesGuard)
export class BrokerController {
  private readonly logger = new Logger(BrokerController.name);

  constructor(
    private readonly brokerService: BrokerService,
    private readonly brokerAuthService: BrokerAuthService,
    private readonly clsService: ClsService,
  ) {}

  // ==================== HELPER METHODS ====================

  /**
   * Get authenticated user information from CLS session
   * @returns User information from session
   * @throws UnauthorizedException if session or user not found
   */
  private getAuthenticatedUser(): { id: string; email: string; role: string } {
    try {
      const session = this.clsService.get('session');

      if (!session || !session.user) {
        this.logger.error('No session or user found in CLS context');
        throw new UnauthorizedException('User authentication required');
      }

      const user = session.user;
      if (!user.id || !user.email) {
        this.logger.error('Incomplete user information in session');
        throw new UnauthorizedException('Invalid user session data');
      }

      return {
        id: user.id,
        email: user.email,
        role: user.role || 'user',
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error('Failed to retrieve user from CLS session:', error);
      throw new UnauthorizedException('Failed to retrieve user session');
    }
  }

  // ==================== CRUD OPERATIONS ====================

  /**
   * Create a new broker configuration
   * Requirements: Admin role required for broker management
   */
  @Post()
  @Roles('admin')
  async createBroker(@Body() dto: CreateBrokerDto): Promise<PublicBroker> {
    this.logger.log(`Creating broker configuration: ${dto.name} (${dto.type}) for user ${dto.userId}`);

    const broker = await this.brokerService.createBroker(dto);

    this.logger.log(`Successfully created broker with ID: ${broker.id}`);
    return broker;
  }

  /**
   * Get all broker configurations with filtering and pagination
   * Requirements: Admin role required to view all brokers
   */
  @Get()
  @Roles('admin')
  async findAllBrokers(
    @Query() filters: BrokerSearchDto,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResult<PublicBroker>> {
    this.logger.log('Finding brokers with filters', { filters, limit, offset, sortBy, sortOrder });

    const paginationOptions: PaginationOptions = {
      limit: limit || 10,
      offset: offset || 0,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'DESC',
      filters: filters as Record<string, unknown>,
    };

    const result = await this.brokerService.findAllBrokers(filters, paginationOptions);

    this.logger.log(`Found ${result.data.length} brokers (${result.total} total)`);
    return result;
  }

  /**
   * Get broker configuration by ID
   * Requirements: Admin and user roles can view broker details
   */
  @Get(':id')
  @Roles('admin', 'user')
  async findBrokerById(@Param('id', ParseIntPipe) id: number): Promise<PublicBroker> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(`Finding broker with ID: ${id} for user: ${user.email}`);

    // Use authenticated user ID for broker access control
    const broker = await this.brokerService.findBrokerById(id, user.id);

    this.logger.log(`Successfully found broker with ID: ${id} for user: ${user.email}`);
    return broker;
  }

  /**
   * Update broker configuration
   * Requirements: Admin role required for broker management
   */
  @Patch(':id')
  @Roles('admin')
  async updateBroker(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateBrokerDto): Promise<PublicBroker> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(`Updating broker configuration ${id} by user: ${user.email}`, { hasApiKey: !!dto.apiKey });

    // Use authenticated user ID for broker updates
    const broker = await this.brokerService.updateBroker(id, user.id, dto);

    this.logger.log(`Successfully updated broker with ID: ${id} by user: ${user.email}`);
    return broker;
  }

  /**
   * Delete broker configuration
   * Requirements: Admin role required for broker management
   */
  @Delete(':id')
  @Roles('admin')
  async deleteBroker(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(`Deleting broker configuration ${id} by user: ${user.email}`);

    // Use authenticated user ID for broker deletion
    const result = await this.brokerService.deleteBroker(id, user.id);

    this.logger.log(`Successfully deleted broker with ID: ${id} by user: ${user.email}`);
    return result;
  }

  // ==================== SPECIALIZED ENDPOINTS ====================

  /**
   * Get active broker configurations for a user
   * Requirements: Admin and user roles can view active brokers
   * Admin users can query any user, regular users can only query themselves
   */
  @Get('active/user/:userId')
  @Roles('admin', 'user')
  async getActiveBrokers(
    @Param('userId') userId: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
  ): Promise<PaginatedResult<PublicBroker>> {
    // Extract user information from CLS session for access control
    const authenticatedUser = this.getAuthenticatedUser();

    // Regular users can only access their own data
    if (authenticatedUser.role !== 'admin' && authenticatedUser.id !== userId) {
      this.logger.warn(`User ${authenticatedUser.email} attempted to access data for user ${userId}`);
      throw new UnauthorizedException('Access denied: You can only access your own broker data');
    }

    this.logger.log(`Getting active brokers for user: ${userId} (requested by: ${authenticatedUser.email})`);

    const paginationOptions: PaginationOptions = {
      limit: limit || 10,
      offset: offset || 0,
      sortBy: 'lastConnectedAt',
      sortOrder: 'DESC',
    };

    const result = await this.brokerService.getActiveBrokers(userId, paginationOptions);

    this.logger.log(`Found ${result.data.length} active brokers for user ${userId}`);
    return result;
  }

  /**
   * Get broker status counts for a user
   * Requirements: Admin and user roles can view broker statistics
   * Admin users can query any user, regular users can only query themselves
   */
  @Get('status/counts/:userId')
  @Roles('admin', 'user')
  async getBrokerStatusCounts(@Param('userId') userId: string): Promise<Record<string, number>> {
    // Extract user information from CLS session for access control
    const authenticatedUser = this.getAuthenticatedUser();

    // Regular users can only access their own data
    if (authenticatedUser.role !== 'admin' && authenticatedUser.id !== userId) {
      this.logger.warn(`User ${authenticatedUser.email} attempted to access status counts for user ${userId}`);
      throw new UnauthorizedException('Access denied: You can only access your own broker statistics');
    }

    this.logger.log(`Getting broker status counts for user: ${userId} (requested by: ${authenticatedUser.email})`);

    const counts = await this.brokerService.getBrokerStatusCounts(userId);

    this.logger.log(`Retrieved broker status counts for user ${userId}`, counts);
    return counts;
  }

  /**
   * Update broker credentials
   * Requirements: Admin role required for credential management
   */
  @Patch(':id/credentials')
  @Roles('admin')
  async updateCredentials(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateBrokerDto): Promise<PublicBroker> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(`Updating credentials for broker ${id} by user: ${user.email}`);

    // Use authenticated user ID for credential updates
    const broker = await this.brokerService.updateCredentials(id, user.id, dto);

    this.logger.log(`Successfully updated credentials for broker ${id} by user: ${user.email}`);
    return broker;
  }

  // ==================== AUTHENTICATION OPERATIONS ====================

  /**
   * Generate Kite Connect login URL
   * Requirements: Authenticated users can initiate broker authentication
   */
  @Post('auth/login-url')
  @Roles('admin', 'user')
  async generateLoginUrl(@Body() dto: GenerateLoginUrlDto): Promise<LoginUrlResponse> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(
      `Generating Kite Connect login URL for user ${user.email} with API key: ${dto.apiKey.substring(0, 8)}...`,
    );

    const result = await this.brokerAuthService.generateLoginUrl(dto);

    this.logger.log(`Successfully generated Kite Connect login URL for user: ${user.email}`);
    return result;
  }

  /**
   * Exchange request token for access token
   * Requirements: Authenticated users can complete broker authentication
   */
  @Post('auth/access-token')
  @Roles('admin', 'user')
  async generateAccessToken(@Body() dto: GenerateAccessTokenDto): Promise<AccessTokenResponse> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(`Exchanging request token for user ${user.email} with API key: ${dto.apiKey.substring(0, 8)}...`);

    const result = await this.brokerAuthService.generateAccessToken(dto);

    this.logger.log(`Successfully generated access token for user: ${user.email} (Kite user: ${result.userId})`);
    return result;
  }

  /**
   * Validate existing session and access token
   * Requirements: Admin and user roles can validate broker sessions
   */
  @Post('auth/validate-session')
  @Roles('admin', 'user')
  async validateSession(@Body() dto: ValidateSessionDto): Promise<SessionValidationResponse> {
    // Extract user information from CLS session
    const user = this.getAuthenticatedUser();

    this.logger.log(`Validating session for user ${user.email} with API key: ${dto.apiKey.substring(0, 8)}...`);

    const result = await this.brokerAuthService.validateSession(dto);

    if (result.isValid) {
      this.logger.log(`Session validation successful for user: ${user.email} (Kite user: ${result.userId})`);
    } else {
      this.logger.warn(
        `Session validation failed for user: ${user.email} with API key: ${dto.apiKey.substring(0, 8)}...`,
      );
    }

    return result;
  }
}
