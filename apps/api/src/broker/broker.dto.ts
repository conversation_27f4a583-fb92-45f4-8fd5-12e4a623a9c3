import { createZodDto } from 'nestjs-zod';
import { CreateBrokerSchema, UpdateBrokerSchema, BrokerSearchSchema } from '@app/broker/broker.schema';
import {
  GenerateLoginUrlSchema,
  GenerateAccessTokenSchema,
  ValidateSessionSchema,
} from '@app/broker/broker-auth.service';

// ==================== BROKER CRUD DTOs ====================

/**
 * DTO for creating a new broker configuration
 * Requirements: Admin role required for broker management
 */
export class CreateBrokerDto extends createZodDto(CreateBrokerSchema) {}

/**
 * DTO for updating an existing broker configuration
 * Requirements: Admin role required for broker management
 */
export class UpdateBrokerDto extends createZodDto(UpdateBrokerSchema) {}

/**
 * DTO for searching and filtering broker configurations
 * Requirements: Admin and user roles can search brokers
 */
export class BrokerSearchDto extends createZodDto(BrokerSearchSchema) {}

// ==================== BROKER AUTHENTICATION DTOs ====================

/**
 * DTO for generating Kite Connect login URL
 * Requirements: Authenticated users can initiate broker authentication
 */
export class GenerateLoginUrlDto extends createZodDto(GenerateLoginUrlSchema) {}

/**
 * DTO for exchanging request token for access token
 * Requirements: Authenticated users can complete broker authentication
 */
export class GenerateAccessTokenDto extends createZodDto(GenerateAccessTokenSchema) {}

/**
 * DTO for validating existing session
 * Requirements: Admin and user roles can validate broker sessions
 */
export class ValidateSessionDto extends createZodDto(ValidateSessionSchema) {}
