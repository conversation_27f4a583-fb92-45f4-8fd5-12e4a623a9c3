import { Module } from '@nestjs/common';
import { CoreModule } from '@app/core';
import { UtilsModule } from '@app/utils';
import { CommonModule } from '@app/common';
import { BrokerService } from './broker.service';
import { BrokerRepository } from './broker.repository';
import { BrokerAuthService } from './broker-auth.service';

/**
 * Broker Module
 *
 * Provides broker credential management functionality including:
 * - Broker configuration CRUD operations
 * - Credential validation and management
 * - User-specific broker associations
 * - Zerodha Kite Connect authentication services
 * - Persistent authentication token management
 * - Secure credential encryption/decryption
 * - Support for multiple broker types (currently Zerodha Kite)
 * - Comprehensive error handling and logging
 *
 * Dependencies:
 * - CoreModule: Database and infrastructure services
 * - UtilsModule: Utility services for validation and formatting
 * - CommonModule: Encryption services for secure credential storage
 */
@Module({
  imports: [CoreModule, UtilsModule, CommonModule],
  providers: [BrokerService, BrokerRepository, BrokerAuthService],
  exports: [BrokerService, BrokerRepository, BrokerAuthService],
})
export class BrokerModule {}
