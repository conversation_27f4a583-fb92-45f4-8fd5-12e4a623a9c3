import { z } from 'zod/v4';
import { BaseError } from '@app/common/errors';
import type { ErrorDomainType } from '@app/common/errors';

// ==================== BROKER ERROR ENUMS ====================

/**
 * Broker error codes enum for comprehensive error handling
 * Covers all broker-related error scenarios
 */
export const BrokerErrorEnum = z.enum([
  // General broker errors
  'BROKER_NOT_FOUND',
  'BROKER_CREATION_FAILED',
  'BROKER_UPDATE_FAILED',
  'BROKER_DELETION_FAILED',
  'BROKER_VALIDATION_FAILED',
  
  // Authentication and credential errors
  'INVALID_CREDENTIALS',
  'AUTHENTICATION_FAILED',
  'TOKEN_EXPIRED',
  'TOKEN_INVALID',
  'TOKEN_REFRESH_FAILED',
  'INSUFFICIENT_PERMISSIONS',
  
  // Connection errors
  'CONNECTION_FAILED',
  'CONNECTION_TIMEOUT',
  'DISCONNECTION_FAILED',
  'RECONNECTION_FAILED',
  'NETWORK_ERROR',
  
  // API and rate limiting errors
  'API_ERROR',
  'RATE_LIMIT_EXCEEDED',
  'REQUEST_TIMEOUT',
  'INVALID_REQUEST',
  'MALFORMED_RESPONSE',
  
  // Trading and market errors
  'MARKET_CLOSED',
  'INVALID_INSTRUMENT',
  'ORDER_REJECTED',
  'INSUFFICIENT_FUNDS',
  'POSITION_ERROR',
  'MARGIN_ERROR',
  
  // Configuration errors
  'INVALID_CONFIGURATION',
  'UNSUPPORTED_BROKER_TYPE',
  'MISSING_REQUIRED_FIELD',
  'INVALID_FIELD_VALUE',
  
  // System errors
  'BROKER_SERVICE_UNAVAILABLE',
  'DATABASE_ERROR',
  'ENCRYPTION_ERROR',
  'DECRYPTION_ERROR',
  'UNKNOWN_ERROR',
]);

/**
 * Broker error messages mapping for consistent error communication
 */
export const BrokerErrorMessages: Record<BrokerErrorEnumType, string> = {
  // General broker errors
  BROKER_NOT_FOUND: 'Broker configuration not found',
  BROKER_CREATION_FAILED: 'Failed to create broker configuration',
  BROKER_UPDATE_FAILED: 'Failed to update broker configuration',
  BROKER_DELETION_FAILED: 'Failed to delete broker configuration',
  BROKER_VALIDATION_FAILED: 'Broker configuration validation failed',
  
  // Authentication and credential errors
  INVALID_CREDENTIALS: 'Invalid broker credentials provided',
  AUTHENTICATION_FAILED: 'Broker authentication failed',
  TOKEN_EXPIRED: 'Broker access token has expired',
  TOKEN_INVALID: 'Invalid broker access token',
  TOKEN_REFRESH_FAILED: 'Failed to refresh broker access token',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions for broker operation',
  
  // Connection errors
  CONNECTION_FAILED: 'Failed to connect to broker',
  CONNECTION_TIMEOUT: 'Connection to broker timed out',
  DISCONNECTION_FAILED: 'Failed to disconnect from broker',
  RECONNECTION_FAILED: 'Failed to reconnect to broker',
  NETWORK_ERROR: 'Network error while communicating with broker',
  
  // API and rate limiting errors
  API_ERROR: 'Broker API error occurred',
  RATE_LIMIT_EXCEEDED: 'Broker API rate limit exceeded',
  REQUEST_TIMEOUT: 'Broker API request timed out',
  INVALID_REQUEST: 'Invalid request sent to broker API',
  MALFORMED_RESPONSE: 'Malformed response received from broker API',
  
  // Trading and market errors
  MARKET_CLOSED: 'Market is currently closed',
  INVALID_INSTRUMENT: 'Invalid trading instrument specified',
  ORDER_REJECTED: 'Order was rejected by broker',
  INSUFFICIENT_FUNDS: 'Insufficient funds for the requested operation',
  POSITION_ERROR: 'Error occurred while processing position data',
  MARGIN_ERROR: 'Error occurred while processing margin data',
  
  // Configuration errors
  INVALID_CONFIGURATION: 'Invalid broker configuration provided',
  UNSUPPORTED_BROKER_TYPE: 'Unsupported broker type specified',
  MISSING_REQUIRED_FIELD: 'Required field is missing from broker configuration',
  INVALID_FIELD_VALUE: 'Invalid value provided for broker configuration field',
  
  // System errors
  BROKER_SERVICE_UNAVAILABLE: 'Broker service is currently unavailable',
  DATABASE_ERROR: 'Database error occurred while processing broker operation',
  ENCRYPTION_ERROR: 'Error occurred while encrypting broker credentials',
  DECRYPTION_ERROR: 'Error occurred while decrypting broker credentials',
  UNKNOWN_ERROR: 'An unknown error occurred in broker service',
};

export type BrokerErrorEnumType = z.output<typeof BrokerErrorEnum>;

// ==================== BROKER ERROR CLASS ====================

/**
 * Broker error class extending BaseError with proper error codes
 * Provides consistent error handling for broker-related operations
 */
export class BrokerError extends BaseError<BrokerErrorEnumType> {
  constructor(
    name: BrokerErrorEnumType, 
    domain: ErrorDomainType, 
    details?: { 
      message?: string; 
      cause?: unknown; 
      context?: Record<string, unknown>;
    }
  ) {
    super({
      name,
      domain,
      message: details?.message ? details.message : BrokerErrorMessages[name],
      cause: details?.cause,
    });
    
    // Attach additional context if provided
    if (details?.context) {
      Object.assign(this, { context: details.context });
    }
    
    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

// ==================== SPECIFIC ERROR CLASSES ====================

/**
 * Broker not found error - thrown when broker configuration doesn't exist
 */
export class BrokerNotFoundError extends BrokerError {
  constructor(brokerId?: number | string, userId?: string) {
    const context: Record<string, unknown> = {};
    if (brokerId) context.brokerId = brokerId;
    if (userId) context.userId = userId;
    
    super('BROKER_NOT_FOUND', 'BROKER', {
      message: brokerId 
        ? `Broker configuration with ID ${brokerId} not found`
        : 'Broker configuration not found',
      context,
    });
  }
}

/**
 * Invalid credentials error - thrown when broker credentials are invalid
 */
export class InvalidCredentialsError extends BrokerError {
  constructor(brokerType?: string, details?: string) {
    const context: Record<string, unknown> = {};
    if (brokerType) context.brokerType = brokerType;
    if (details) context.details = details;
    
    super('INVALID_CREDENTIALS', 'BROKER', {
      message: brokerType 
        ? `Invalid credentials for ${brokerType} broker${details ? ': ' + details : ''}`
        : 'Invalid broker credentials provided',
      context,
    });
  }
}

/**
 * Broker connection error - thrown when connection to broker fails
 */
export class BrokerConnectionError extends BrokerError {
  constructor(brokerType?: string, errorMessage?: string, cause?: unknown) {
    const context: Record<string, unknown> = {};
    if (brokerType) context.brokerType = brokerType;
    if (errorMessage) context.originalError = errorMessage;
    
    super('CONNECTION_FAILED', 'BROKER', {
      message: brokerType 
        ? `Failed to connect to ${brokerType} broker${errorMessage ? ': ' + errorMessage : ''}`
        : 'Failed to connect to broker',
      cause,
      context,
    });
  }
}

/**
 * Authentication failed error - thrown when broker authentication fails
 */
export class AuthenticationFailedError extends BrokerError {
  constructor(brokerType?: string, reason?: string) {
    const context: Record<string, unknown> = {};
    if (brokerType) context.brokerType = brokerType;
    if (reason) context.reason = reason;
    
    super('AUTHENTICATION_FAILED', 'BROKER', {
      message: brokerType 
        ? `Authentication failed for ${brokerType} broker${reason ? ': ' + reason : ''}`
        : 'Broker authentication failed',
      context,
    });
  }
}

/**
 * Token expired error - thrown when access token has expired
 */
export class TokenExpiredError extends BrokerError {
  constructor(brokerType?: string, expiryTime?: Date) {
    const context: Record<string, unknown> = {};
    if (brokerType) context.brokerType = brokerType;
    if (expiryTime) context.expiryTime = expiryTime.toISOString();
    
    super('TOKEN_EXPIRED', 'BROKER', {
      message: brokerType 
        ? `Access token expired for ${brokerType} broker`
        : 'Broker access token has expired',
      context,
    });
  }
}

/**
 * Rate limit exceeded error - thrown when API rate limits are exceeded
 */
export class RateLimitExceededError extends BrokerError {
  constructor(brokerType?: string, retryAfter?: number) {
    const context: Record<string, unknown> = {};
    if (brokerType) context.brokerType = brokerType;
    if (retryAfter) context.retryAfter = retryAfter;
    
    super('RATE_LIMIT_EXCEEDED', 'BROKER', {
      message: brokerType 
        ? `Rate limit exceeded for ${brokerType} broker${retryAfter ? `. Retry after ${retryAfter} seconds` : ''}`
        : 'Broker API rate limit exceeded',
      context,
    });
  }
}

/**
 * Market closed error - thrown when attempting operations during market closure
 */
export class MarketClosedError extends BrokerError {
  constructor(marketName?: string, nextOpenTime?: Date) {
    const context: Record<string, unknown> = {};
    if (marketName) context.marketName = marketName;
    if (nextOpenTime) context.nextOpenTime = nextOpenTime.toISOString();
    
    super('MARKET_CLOSED', 'BROKER', {
      message: marketName 
        ? `${marketName} market is currently closed${nextOpenTime ? `. Next open: ${nextOpenTime.toISOString()}` : ''}`
        : 'Market is currently closed',
      context,
    });
  }
}

/**
 * Invalid instrument error - thrown when instrument is not valid or supported
 */
export class InvalidInstrumentError extends BrokerError {
  constructor(instrument?: string, brokerType?: string) {
    const context: Record<string, unknown> = {};
    if (instrument) context.instrument = instrument;
    if (brokerType) context.brokerType = brokerType;
    
    super('INVALID_INSTRUMENT', 'BROKER', {
      message: instrument 
        ? `Invalid instrument: ${instrument}${brokerType ? ` for ${brokerType} broker` : ''}`
        : 'Invalid trading instrument specified',
      context,
    });
  }
}

// ==================== ERROR FACTORY FUNCTIONS ====================

/**
 * Create a broker error with context
 */
export function createBrokerError(
  errorType: BrokerErrorEnumType,
  context?: Record<string, unknown>,
  message?: string,
  cause?: unknown,
): BrokerError {
  return new BrokerError(errorType, 'BROKER', {
    message,
    cause,
    context,
  });
}

/**
 * Create a Kite-specific broker error
 */
export function createKiteBrokerError(
  errorType: BrokerErrorEnumType,
  kiteErrorCode?: string,
  kiteErrorMessage?: string,
  cause?: unknown,
): BrokerError {
  const context: Record<string, unknown> = {
    brokerType: 'ZERODHA_KITE',
  };
  
  if (kiteErrorCode) context.kiteErrorCode = kiteErrorCode;
  if (kiteErrorMessage) context.kiteErrorMessage = kiteErrorMessage;
  
  return new BrokerError(errorType, 'KITE_BROKER', {
    message: kiteErrorMessage || BrokerErrorMessages[errorType],
    cause,
    context,
  });
}
