import { pgTable, varchar, text, boolean, index, unique } from 'drizzle-orm/pg-core';
import { baseModel } from '@app/common/models';

// ==================== DATABASE SCHEMA ====================

/**
 * Broker credentials table schema for PatternTrade platform
 * Stores broker authentication credentials and configuration
 *
 * Features:
 * - Secure credential storage for broker APIs
 * - Support for multiple broker types (currently Zerodha Kite)
 * - User-specific broker configurations
 * - Status tracking for credential validation
 * - Comprehensive audit trail
 * - Optimized indexing for performance
 * - Unique constraints for data integrity
 */
export const BrokerTable = pgTable(
  'brokers',
  {
    ...baseModel,

    // Core broker identification
    name: varchar('name', { length: 100 }).notNull(),
    type: varchar('type', { length: 50 }).notNull(), // BrokerTypeEnum values

    // User association
    userId: varchar('user_id', { length: 255 }).notNull(),

    // Broker credentials (encrypted in production)
    apiKey: varchar('api_key', { length: 255 }).notNull(),
    apiSecret: text('api_secret').notNull(), // Encrypted storage
    accessToken: text('access_token'), // Optional, obtained after authentication

    // Status and configuration
    status: varchar('status', { length: 20 }).notNull().default('INACTIVE'), // BrokerStatusEnum values
    isActive: boolean('is_active').notNull().default(true),

    // Additional metadata
    lastConnectedAt: varchar('last_connected_at', { length: 32 }),
    lastErrorMessage: text('last_error_message'),
    connectionAttempts: varchar('connection_attempts', { length: 10 }).default('0'),

    // Configuration settings (JSON string)
    settings: text('settings'), // JSON configuration for broker-specific settings
  },
  (table) => [
    // Unique constraints
    unique('unique_user_broker_type').on(table.userId, table.type),
    unique('unique_user_broker_name').on(table.userId, table.name),

    // Performance indexes
    index('idx_broker_user_id').on(table.userId),
    index('idx_broker_type').on(table.type),
    index('idx_broker_status').on(table.status),
    index('idx_broker_active').on(table.isActive),

    // Composite indexes for common queries
    index('idx_broker_user_active').on(table.userId, table.isActive),
    index('idx_broker_user_type').on(table.userId, table.type),
    index('idx_broker_user_status').on(table.userId, table.status),
    index('idx_broker_type_status').on(table.type, table.status),

    // Audit indexes
    index('idx_broker_created_at').on(table.createdAt),
    index('idx_broker_updated_at').on(table.updatedAt),
    index('idx_broker_last_connected').on(table.lastConnectedAt),
  ],
);

// ==================== TYPE EXPORTS ====================

/**
 * Type for broker table select operations
 * Represents the complete broker record as stored in database
 */
export type BrokerTableSelect = typeof BrokerTable.$inferSelect;

/**
 * Type for broker table insert operations
 * Represents the data required to create a new broker record
 */
export type BrokerTableInsert = typeof BrokerTable.$inferInsert;

// ==================== TABLE METADATA ====================

/**
 * Broker table metadata for repository operations
 */
export const BROKER_TABLE_METADATA = {
  tableName: 'brokers',
  primaryKey: 'id',
  uniqueConstraints: [
    ['userId', 'type'], // One broker per type per user
    ['userId', 'name'], // Unique broker names per user
  ],
  indexes: ['userId', 'type', 'status', 'isActive', 'createdAt', 'updatedAt', 'lastConnectedAt'],
  auditFields: ['createdAt', 'createdBy', 'updatedAt', 'updatedBy', 'deletedAt', 'deletedBy'],
  encryptedFields: [
    'apiSecret', // Always encrypted
    'accessToken', // Encrypted when present
  ],
} as const;

// ==================== QUERY HELPERS ====================

/**
 * Common query conditions for broker operations
 */
export const BROKER_QUERY_CONDITIONS = {
  active: { isActive: true },
  inactive: { isActive: false },
  notDeleted: { deletedAt: null },
  connected: { status: 'ACTIVE' },
  hasAccessToken: { accessToken: { not: null } },
} as const;

/**
 * Broker status values for validation
 */
export const BROKER_STATUS_VALUES = ['ACTIVE', 'INACTIVE', 'EXPIRED', 'INVALID', 'SUSPENDED', 'ERROR'] as const;

/**
 * Broker type values for validation
 */
export const BROKER_TYPE_VALUES = ['ZERODHA_KITE'] as const;

// ==================== SECURITY HELPERS ====================

/**
 * Fields that should be excluded from public API responses
 */
export const BROKER_SENSITIVE_FIELDS = ['apiSecret', 'accessToken'] as const;

/**
 * Fields that should be included in audit logs
 */
export const BROKER_AUDIT_FIELDS = [
  'id',
  'name',
  'type',
  'userId',
  'status',
  'isActive',
  'lastConnectedAt',
  'connectionAttempts',
] as const;

/**
 * Default broker settings by type
 */
export const DEFAULT_BROKER_SETTINGS = {
  ZERODHA_KITE: {
    autoReconnect: true,
    maxRetryAttempts: 3,
    retryDelay: 1000,
    subscriptionMode: 'quote',
    enableOrderUpdates: true,
    enablePositionUpdates: true,
  },
} as const;

// ==================== VALIDATION HELPERS ====================

/**
 * Validate broker type
 */
export function isValidBrokerType(type: string): type is (typeof BROKER_TYPE_VALUES)[number] {
  return BROKER_TYPE_VALUES.includes(type as (typeof BROKER_TYPE_VALUES)[number]);
}

/**
 * Validate broker status
 */
export function isValidBrokerStatus(status: string): status is (typeof BROKER_STATUS_VALUES)[number] {
  return BROKER_STATUS_VALUES.includes(status as (typeof BROKER_STATUS_VALUES)[number]);
}

/**
 * Get default settings for broker type
 */
export function getDefaultBrokerSettings(type: string): Record<string, unknown> {
  if (type === 'ZERODHA_KITE') {
    return DEFAULT_BROKER_SETTINGS.ZERODHA_KITE;
  }
  return {};
}

/**
 * Check if broker has valid credentials
 */
export function hasValidCredentials(broker: BrokerTableSelect): boolean {
  return !!(broker.apiKey && broker.apiSecret && broker.status === 'ACTIVE' && broker.isActive);
}

/**
 * Check if broker is ready for trading
 */
export function isReadyForTrading(broker: BrokerTableSelect): boolean {
  return !!(hasValidCredentials(broker) && broker.accessToken && broker.status === 'ACTIVE');
}
