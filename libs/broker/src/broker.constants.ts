import { z } from 'zod/v4';

// ==================== BROKER ENUMS ====================

/**
 * Broker type enum for supported brokers
 * Currently focused on Zerodha Kite for single broker architecture
 */
export const BrokerTypeEnum = z.enum(['ZERODHA_KITE']);

/**
 * Broker status enum for credential and connection status
 */
export const BrokerStatusEnum = z.enum([
  'ACTIVE', // Broker credentials are valid and active
  'INACTIVE', // Broker credentials are disabled
  'EXPIRED', // Access token has expired
  'INVALID', // Credentials are invalid
  'SUSPENDED', // Broker account is suspended
  'ERROR', // Connection or authentication error
]);

/**
 * Broker connection status enum for real-time status tracking
 */
export const BrokerConnectionStatusEnum = z.enum([
  'CONNECTED', // Successfully connected to broker
  'CONNECTING', // Attempting to connect
  'DISCONNECTED', // Not connected
  'RECONNECTING', // Attempting to reconnect
  'FAILED', // Connection failed
]);

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types from Zod schemas

export type BrokerType = z.output<typeof BrokerTypeEnum>;
export type BrokerStatus = z.output<typeof BrokerStatusEnum>;
export type BrokerConnectionStatus = z.output<typeof BrokerConnectionStatusEnum>;

// ==================== ZERODHA KITE CONSTANTS ====================

/**
 * Zerodha Kite Connect API configuration
 * Based on official Kite Connect documentation
 */
export const ZERODHA_KITE_CONFIG = {
  name: 'Zerodha Kite',
  type: 'ZERODHA_KITE' as const,
  baseUrl: 'https://api.kite.trade',
  websocketUrl: 'wss://ws.kite.trade',
  loginUrl: 'https://kite.zerodha.com/connect/login',

  // API endpoints
  endpoints: {
    profile: '/user/profile',
    margins: '/user/margins',
    positions: '/portfolio/positions',
    holdings: '/portfolio/holdings',
    orders: '/orders',
    trades: '/trades',
    instruments: '/instruments',
    quote: '/quote',
    ohlc: '/quote/ohlc',
    ltp: '/quote/ltp',
    historical: '/instruments/historical',
  },

  // Rate limits and timeouts
  limits: {
    maxOrdersPerSecond: 10,
    maxSubscriptions: 3000,
    maxHistoricalDays: 2000,
    connectionTimeout: 30000, // 30 seconds
    requestTimeout: 10000, // 10 seconds
    websocketTimeout: 5000, // 5 seconds
    maxRetryAttempts: 3,
    retryDelay: 1000, // 1 second
  },

  // Supported exchanges
  exchanges: ['NSE', 'BSE', 'NFO', 'BFO', 'CDS', 'MCX'] as const,

  // Supported segments
  segments: ['NSE', 'BSE', 'NFO-FUT', 'NFO-OPT', 'BFO-FUT', 'BFO-OPT', 'CDS', 'MCX'] as const,

  // Subscription modes for WebSocket
  subscriptionModes: ['ltp', 'quote', 'full'] as const,

  // Order types
  orderTypes: ['MARKET', 'LIMIT', 'SL', 'SL-M'] as const,

  // Transaction types
  transactionTypes: ['BUY', 'SELL'] as const,

  // Product types
  productTypes: ['CNC', 'MIS', 'NRML'] as const,

  // Validity types
  validityTypes: ['DAY', 'IOC'] as const,
} as const;

/**
 * Kite WebSocket message types for real-time data
 */
export const KITE_WS_MESSAGE_TYPES = {
  CONNECT: 'connect',
  SUBSCRIBE: 'subscribe',
  UNSUBSCRIBE: 'unsubscribe',
  TICK: 'tick',
  ERROR: 'error',
  RECONNECT: 'reconnect',
  ORDER_UPDATE: 'order',
} as const;

/**
 * Kite API error codes mapping
 */
export const KITE_ERROR_CODES = {
  INVALID_API_KEY: 'Invalid API key',
  INVALID_ACCESS_TOKEN: 'Invalid access token',
  TOKEN_EXPIRED: 'Token expired',
  INSUFFICIENT_FUNDS: 'Insufficient funds',
  INVALID_INSTRUMENT: 'Invalid instrument',
  MARKET_CLOSED: 'Market is closed',
  ORDER_REJECTED: 'Order rejected',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
  NETWORK_ERROR: 'Network error',
  UNKNOWN_ERROR: 'Unknown error',
} as const;

// ==================== BROKER OPERATION CONSTANTS ====================

/**
 * Default configuration values for broker operations
 */
export const BROKER_DEFAULTS = {
  // Connection settings
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  REQUEST_TIMEOUT: 10000, // 10 seconds
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second

  // Session management
  SESSION_TIMEOUT: 3600000, // 1 hour
  TOKEN_REFRESH_INTERVAL: 3300000, // 55 minutes (before 1 hour expiry)

  // Health check settings
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  HEALTH_CHECK_TIMEOUT: 5000, // 5 seconds

  // Rate limiting
  RATE_LIMIT_REQUESTS: 100,
  RATE_LIMIT_WINDOW: 60000, // 1 minute

  // Data refresh intervals
  POSITION_REFRESH_INTERVAL: 5000, // 5 seconds
  MARGIN_REFRESH_INTERVAL: 10000, // 10 seconds
  HOLDING_REFRESH_INTERVAL: 30000, // 30 seconds
} as const;

/**
 * Broker validation rules and constraints
 */
export const BROKER_VALIDATION = {
  // Field length constraints
  NAME_MIN_LENGTH: 1,
  NAME_MAX_LENGTH: 100,
  API_KEY_LENGTH: 32, // Kite API key length
  API_SECRET_LENGTH: 32, // Kite API secret length
  ACCESS_TOKEN_MIN_LENGTH: 32, // Minimum access token length
  ACCESS_TOKEN_MAX_LENGTH: 256, // Maximum access token length

  // Validation patterns
  API_KEY_PATTERN: /^[a-z0-9]{32}$/,
  API_SECRET_PATTERN: /^[a-z0-9]{32}$/,
  ACCESS_TOKEN_PATTERN: /^[a-zA-Z0-9_-]+$/,
} as const;

/**
 * Broker error messages for consistent error handling
 */
export const BROKER_ERROR_MESSAGES = {
  BROKER_NOT_FOUND: 'Broker configuration not found',
  INVALID_CREDENTIALS: 'Invalid broker credentials',
  CONNECTION_FAILED: 'Failed to connect to broker',
  AUTHENTICATION_FAILED: 'Broker authentication failed',
  TOKEN_EXPIRED: 'Broker access token has expired',
  RATE_LIMIT_EXCEEDED: 'Broker API rate limit exceeded',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions for broker operation',
  MARKET_CLOSED: 'Market is currently closed',
  INVALID_INSTRUMENT: 'Invalid trading instrument',
  ORDER_REJECTED: 'Order was rejected by broker',
  INSUFFICIENT_FUNDS: 'Insufficient funds for order',
  NETWORK_ERROR: 'Network error while communicating with broker',
  UNKNOWN_ERROR: 'Unknown broker error occurred',
} as const;

// ==================== UTILITY FUNCTIONS ====================

/**
 * Get broker configuration by type
 */
export function getBrokerConfig(brokerType: BrokerType) {
  switch (brokerType) {
    case 'ZERODHA_KITE':
      return ZERODHA_KITE_CONFIG;
    default:
      throw new Error(`Unsupported broker type: ${brokerType}`);
  }
}

/**
 * Validate broker credentials format
 */
export function validateBrokerCredentials(
  brokerType: BrokerType,
  credentials: {
    apiKey: string;
    apiSecret: string;
    accessToken?: string;
  },
): boolean {
  const config = getBrokerConfig(brokerType);

  switch (brokerType) {
    case 'ZERODHA_KITE':
      return (
        BROKER_VALIDATION.API_KEY_PATTERN.test(credentials.apiKey) &&
        BROKER_VALIDATION.API_SECRET_PATTERN.test(credentials.apiSecret) &&
        (!credentials.accessToken || BROKER_VALIDATION.ACCESS_TOKEN_PATTERN.test(credentials.accessToken))
      );
    default:
      return false;
  }
}

/**
 * Get default broker status based on credentials
 */
export function getDefaultBrokerStatus(hasAccessToken: boolean): BrokerStatus {
  return hasAccessToken ? 'ACTIVE' : 'INACTIVE';
}
