import { Injectable, Logger } from '@nestjs/common';
import { eq, and, like, isNull, isNotNull, desc, asc, count } from 'drizzle-orm';
import { BaseRepository, PaginationOptions, PaginatedResult } from '@app/common/repository';
import { DrizzleService } from '@app/core/drizzle';
import { DateTimeUtilsService, DbUtilsService, UtilsService } from '@app/utils';
import { BrokerTable, BrokerTableSelect } from './broker.model';
import { BrokerSearch, CreateBroker, UpdateBroker } from './broker.schema';
import { BrokerType, BrokerStatus } from './broker.constants';
import { BrokerNotFoundError, createBrokerError } from './broker.error';

// ==================== BROKER REPOSITORY ====================

/**
 * Broker repository for database operations
 * Extends BaseRepository with broker-specific methods
 */
@Injectable()
export class BrokerRepository extends BaseRepository<BrokerTableSelect, CreateBroker, UpdateBroker> {
  protected readonly logger = new Logger(BrokerRepository.name);

  constructor(
    protected readonly drizzleService: DrizzleService,
    protected readonly dateTimeUtils: DateTimeUtilsService,
    protected readonly dbUtils: DbUtilsService,
    protected readonly utils: UtilsService,
  ) {
    super(drizzleService, utils, dbUtils, BrokerRepository.name);
  }

  // ==================== BASE REPOSITORY IMPLEMENTATION ====================

  protected getTable() {
    return BrokerTable;
  }

  protected getTableName(): string {
    return 'brokers';
  }

  // ==================== BROKER-SPECIFIC METHODS ====================

  /**
   * Find broker by ID with user validation
   */
  async findByIdAndUserId(id: number, userId: string): Promise<BrokerTableSelect | null> {
    return this.executeWithErrorHandling('findByIdAndUserId', async () => {
      this.logOperation('findByIdAndUserId', { id, userId });

      const [broker] = await this.getDb()
        .select()
        .from(BrokerTable)
        .where(and(eq(BrokerTable.id, id), eq(BrokerTable.userId, userId), isNull(BrokerTable.deletedAt)))
        .limit(1);

      if (!broker) {
        this.logger.warn(`Broker with ID ${id} not found for user ${userId}`);
        return null;
      }

      this.logger.log(`Found broker with ID ${id} for user ${userId}`);
      return broker;
    });
  }

  /**
   * Find brokers by type
   */
  async findByType(type: BrokerType, options?: PaginationOptions): Promise<PaginatedResult<BrokerTableSelect>> {
    return this.executeWithErrorHandling('findByType', async () => {
      this.logOperation('findByType', { type, options });

      const { limit = 10, offset = 0, sortBy = 'createdAt', sortOrder = 'DESC' } = options || {};

      // Build WHERE conditions
      const conditions = [eq(BrokerTable.type, type), isNull(BrokerTable.deletedAt)];

      // Build ORDER BY clause
      const sortColumn =
        sortBy === 'createdAt'
          ? BrokerTable.createdAt
          : sortBy === 'updatedAt'
            ? BrokerTable.updatedAt
            : sortBy === 'name'
              ? BrokerTable.name
              : BrokerTable.createdAt;
      const orderByClause = sortOrder === 'ASC' ? asc(sortColumn) : desc(sortColumn);

      // Execute data query
      const data = await this.getDb()
        .select()
        .from(BrokerTable)
        .where(and(...conditions))
        .orderBy(orderByClause)
        .limit(limit)
        .offset(offset);

      // Execute count query
      const totalResult = await this.getDb()
        .select({ count: count() })
        .from(BrokerTable)
        .where(and(...conditions));

      const total = totalResult[0]?.count || 0;
      const hasMore = offset + limit < total;

      return {
        data: data as BrokerTableSelect[],
        total,
        hasMore,
        limit,
        offset,
      };
    });
  }

  /**
   * Find active brokers by user ID
   */
  async findActiveByUserId(userId: string, options?: PaginationOptions): Promise<PaginatedResult<BrokerTableSelect>> {
    return this.executeWithErrorHandling('findActiveByUserId', async () => {
      this.logOperation('findActiveByUserId', { userId, options });

      const { limit = 10, offset = 0 } = options || {};

      // Build WHERE conditions
      const conditions = [
        eq(BrokerTable.userId, userId),
        eq(BrokerTable.isActive, true),
        isNull(BrokerTable.deletedAt),
      ];

      // Execute data query
      const data = await this.getDb()
        .select()
        .from(BrokerTable)
        .where(and(...conditions))
        .orderBy(desc(BrokerTable.lastConnectedAt), desc(BrokerTable.updatedAt))
        .limit(limit)
        .offset(offset);

      // Execute count query
      const totalResult = await this.getDb()
        .select({ count: count() })
        .from(BrokerTable)
        .where(and(...conditions));

      const total = totalResult[0]?.count || 0;
      const hasMore = offset + limit < total;

      return {
        data: data as BrokerTableSelect[],
        total,
        hasMore,
        limit,
        offset,
      };
    });
  }

  /**
   * Find broker by user ID and type
   */
  async findByUserIdAndType(userId: string, type: BrokerType): Promise<BrokerTableSelect | null> {
    return this.executeWithErrorHandling('findByUserIdAndType', async () => {
      this.logOperation('findByUserIdAndType', { userId, type });

      const [broker] = await this.getDb()
        .select()
        .from(BrokerTable)
        .where(and(eq(BrokerTable.userId, userId), eq(BrokerTable.type, type), isNull(BrokerTable.deletedAt)))
        .limit(1);

      if (!broker) {
        this.logger.warn(`No ${type} broker found for user ${userId}`);
        return null;
      }

      this.logger.log(`Found ${type} broker for user ${userId}`);
      return broker;
    });
  }

  /**
   * Find brokers with access tokens
   */
  async findWithAccessTokens(
    userId?: string,
    options?: PaginationOptions,
  ): Promise<PaginatedResult<BrokerTableSelect>> {
    return this.executeWithErrorHandling('findWithAccessTokens', async () => {
      this.logOperation('findWithAccessTokens', { userId, options });

      const { limit = 10, offset = 0 } = options || {};

      const conditions = [
        isNotNull(BrokerTable.accessToken),
        eq(BrokerTable.isActive, true),
        isNull(BrokerTable.deletedAt),
      ];

      if (userId) {
        conditions.push(eq(BrokerTable.userId, userId));
      }

      // Execute data query
      const data = await this.getDb()
        .select()
        .from(BrokerTable)
        .where(and(...conditions))
        .orderBy(desc(BrokerTable.lastConnectedAt))
        .limit(limit)
        .offset(offset);

      // Execute count query
      const totalResult = await this.getDb()
        .select({ count: count() })
        .from(BrokerTable)
        .where(and(...conditions));

      const total = totalResult[0]?.count || 0;
      const hasMore = offset + limit < total;

      return {
        data: data as BrokerTableSelect[],
        total,
        hasMore,
        limit,
        offset,
      };
    });
  }

  /**
   * Update broker credentials
   */
  async updateCredentials(
    id: number,
    userId: string,
    credentials: {
      apiKey?: string;
      apiSecret?: string;
      accessToken?: string;
      status?: BrokerStatus;
    },
  ): Promise<BrokerTableSelect> {
    return this.executeWithErrorHandling('updateCredentials', async () => {
      this.logOperation('updateCredentials', { id, userId, hasApiKey: !!credentials.apiKey });

      // First verify the broker exists and belongs to the user
      const existingBroker = await this.findByIdAndUserId(id, userId);
      if (!existingBroker) {
        throw new BrokerNotFoundError(id, userId);
      }

      // Prepare update data with audit fields
      const updateData = this.addUpdateAuditFields({
        ...credentials,
        connectionAttempts: credentials.status === 'ACTIVE' ? '0' : existingBroker.connectionAttempts,
        lastConnectedAt:
          credentials.status === 'ACTIVE' ? this.dateTimeUtils.getUtcNow() : existingBroker.lastConnectedAt,
      });

      const [updatedBroker] = await this.getDb()
        .update(BrokerTable)
        .set(updateData)
        .where(and(eq(BrokerTable.id, id), eq(BrokerTable.userId, userId), isNull(BrokerTable.deletedAt)))
        .returning();

      if (!updatedBroker) {
        throw createBrokerError('BROKER_UPDATE_FAILED', { brokerId: id, userId });
      }

      this.logger.log(`Updated credentials for broker ${id}`);
      return updatedBroker;
    });
  }

  /**
   * Update broker status and connection info
   */
  async updateConnectionStatus(
    id: number,
    status: BrokerStatus,
    errorMessage?: string,
    incrementAttempts: boolean = false,
  ): Promise<BrokerTableSelect> {
    return this.executeWithErrorHandling('updateConnectionStatus', async () => {
      this.logOperation('updateConnectionStatus', { id, status, hasError: !!errorMessage });

      // Get current broker to calculate new attempt count
      const currentBroker = await this.findById(id);
      if (!currentBroker) {
        throw new BrokerNotFoundError(id);
      }

      const currentAttempts = parseInt(currentBroker.connectionAttempts || '0', 10);
      const newAttempts = incrementAttempts ? currentAttempts + 1 : currentAttempts;

      const updateData = this.addUpdateAuditFields({
        status,
        lastErrorMessage: errorMessage || null,
        connectionAttempts: newAttempts.toString(),
        lastConnectedAt: status === 'ACTIVE' ? this.dateTimeUtils.getUtcNow() : currentBroker.lastConnectedAt,
      });

      const [updatedBroker] = await this.getDb()
        .update(BrokerTable)
        .set(updateData)
        .where(and(eq(BrokerTable.id, id), isNull(BrokerTable.deletedAt)))
        .returning();

      if (!updatedBroker) {
        throw createBrokerError('BROKER_UPDATE_FAILED', { brokerId: id });
      }

      this.logger.log(`Updated connection status for broker ${id} to ${status}`);
      return updatedBroker;
    });
  }

  /**
   * Search brokers with filters
   */
  async search(filters: BrokerSearch, options?: PaginationOptions): Promise<PaginatedResult<BrokerTableSelect>> {
    return this.executeWithErrorHandling('search', async () => {
      this.logOperation('search', { filters, options });

      const { limit = 10, offset = 0, sortBy = 'updatedAt', sortOrder = 'DESC' } = options || {};

      const conditions = [isNull(BrokerTable.deletedAt)];

      // Apply filters
      if (filters.userId) {
        conditions.push(eq(BrokerTable.userId, filters.userId));
      }

      if (filters.type) {
        conditions.push(eq(BrokerTable.type, filters.type));
      }

      if (filters.status) {
        conditions.push(eq(BrokerTable.status, filters.status));
      }

      if (filters.isActive !== undefined) {
        conditions.push(eq(BrokerTable.isActive, filters.isActive));
      }

      if (filters.hasAccessToken !== undefined) {
        conditions.push(filters.hasAccessToken ? isNotNull(BrokerTable.accessToken) : isNull(BrokerTable.accessToken));
      }

      if (filters.name) {
        conditions.push(like(BrokerTable.name, `%${filters.name}%`));
      }

      // Build ORDER BY clause
      const sortColumn =
        sortBy === 'createdAt'
          ? BrokerTable.createdAt
          : sortBy === 'updatedAt'
            ? BrokerTable.updatedAt
            : sortBy === 'name'
              ? BrokerTable.name
              : BrokerTable.updatedAt;
      const orderByClause = sortOrder === 'ASC' ? asc(sortColumn) : desc(sortColumn);

      // Execute data query
      const data = await this.getDb()
        .select()
        .from(BrokerTable)
        .where(and(...conditions))
        .orderBy(orderByClause)
        .limit(limit)
        .offset(offset);

      // Execute count query
      const totalResult = await this.getDb()
        .select({ count: count() })
        .from(BrokerTable)
        .where(and(...conditions));

      const total = totalResult[0]?.count || 0;
      const hasMore = offset + limit < total;

      return {
        data: data as BrokerTableSelect[],
        total,
        hasMore,
        limit,
        offset,
      };
    });
  }

  /**
   * Count brokers by status for a user
   */
  async countByUserAndStatus(userId: string): Promise<Record<BrokerStatus, number>> {
    return this.executeWithErrorHandling('countByUserAndStatus', async () => {
      this.logOperation('countByUserAndStatus', { userId });

      const brokers = await this.getDb()
        .select({
          status: BrokerTable.status,
        })
        .from(BrokerTable)
        .where(and(eq(BrokerTable.userId, userId), eq(BrokerTable.isActive, true), isNull(BrokerTable.deletedAt)));

      // Initialize counts
      const counts: Record<BrokerStatus, number> = {
        ACTIVE: 0,
        INACTIVE: 0,
        EXPIRED: 0,
        INVALID: 0,
        SUSPENDED: 0,
        ERROR: 0,
      };

      // Count by status
      brokers.forEach((broker) => {
        const status = broker.status as BrokerStatus;
        if (status in counts) {
          counts[status]++;
        }
      });

      this.logger.log(`Counted brokers by status for user ${userId}:`, counts);
      return counts;
    });
  }

  /**
   * Check if user has broker of specific type
   */
  async hasUserBrokerOfType(userId: string, type: BrokerType): Promise<boolean> {
    return this.executeWithErrorHandling('hasUserBrokerOfType', async () => {
      this.logOperation('hasUserBrokerOfType', { userId, type });

      const [broker] = await this.getDb()
        .select({ id: BrokerTable.id })
        .from(BrokerTable)
        .where(and(eq(BrokerTable.userId, userId), eq(BrokerTable.type, type), isNull(BrokerTable.deletedAt)))
        .limit(1);

      const exists = !!broker;
      this.logger.log(`User ${userId} ${exists ? 'has' : 'does not have'} ${type} broker`);
      return exists;
    });
  }
}
