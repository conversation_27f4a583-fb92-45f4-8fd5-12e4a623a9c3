import { Injectable, Logger } from '@nestjs/common';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { BrokerRepository } from './broker.repository';
import { CreateBroker, UpdateBroker, BrokerSearch, PublicBroker } from './broker.schema';
import { BrokerType, BrokerStatus, validateBrokerCredentials } from './broker.constants';
import { BrokerNotFoundError, InvalidCredentialsError, createBrokerError } from './broker.error';
import { BrokerTableSelect } from './broker.model';

/**
 * Broker service for business logic operations
 * Handles broker credential management and validation
 */
@Injectable()
export class BrokerService {
  private readonly logger = new Logger(BrokerService.name);

  constructor(
    private readonly brokerRepository: BrokerRepository,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== CRUD OPERATIONS ====================

  /**
   * Create a new broker configuration
   */
  async createBroker(data: CreateBroker): Promise<PublicBroker> {
    try {
      this.logger.log(`Creating broker configuration: ${data.name} (${data.type}) for user ${data.userId}`);

      // Validate broker type is supported
      if (!this.isSupportedBrokerType(data.type)) {
        throw createBrokerError('UNSUPPORTED_BROKER_TYPE', { brokerType: data.type });
      }

      // Check if user already has a broker of this type
      const existingBroker = await this.brokerRepository.findByUserIdAndType(data.userId, data.type);
      if (existingBroker) {
        throw createBrokerError('BROKER_CREATION_FAILED', {
          userId: data.userId,
          brokerType: data.type,
          reason: 'User already has a broker of this type',
        });
      }

      // Validate credentials format
      if (
        !validateBrokerCredentials(data.type, {
          apiKey: data.apiKey,
          apiSecret: data.apiSecret,
          accessToken: data.accessToken,
        })
      ) {
        throw new InvalidCredentialsError(data.type, 'Invalid credential format');
      }

      // Create broker with default settings
      const brokerData: CreateBroker = {
        name: data.name,
        type: data.type,
        userId: data.userId,
        apiKey: data.apiKey,
        apiSecret: data.apiSecret,
        accessToken: data.accessToken,
        settings: data.settings,
      };

      const createdBroker = await this.brokerRepository.create(brokerData);

      this.logger.log(`Successfully created broker with ID: ${createdBroker.id}`);
      return this.toPublicBroker(createdBroker);
    } catch (error) {
      this.logger.error('Failed to create broker configuration', {
        data: { ...data, apiSecret: '[REDACTED]', accessToken: '[REDACTED]' },
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Update an existing broker configuration
   */
  async updateBroker(id: number, userId: string, data: UpdateBroker): Promise<PublicBroker> {
    try {
      this.logger.log(`Updating broker configuration ${id} for user ${userId}`);

      // Verify broker exists and belongs to user
      const existingBroker = await this.brokerRepository.findByIdAndUserId(id, userId);
      if (!existingBroker) {
        throw new BrokerNotFoundError(id, userId);
      }

      // Validate credentials if provided
      if (data.apiKey || data.apiSecret) {
        const credentials = {
          apiKey: data.apiKey || existingBroker.apiKey,
          apiSecret: data.apiSecret || existingBroker.apiSecret,
          accessToken: data.accessToken || existingBroker.accessToken || undefined,
        };

        if (!validateBrokerCredentials(existingBroker.type as BrokerType, credentials)) {
          throw new InvalidCredentialsError(existingBroker.type, 'Invalid credential format');
        }
      }

      const updatedBroker = await this.brokerRepository.update(id, data);

      this.logger.log(`Successfully updated broker with ID: ${id}`);
      return this.toPublicBroker(updatedBroker);
    } catch (error) {
      this.logger.error('Failed to update broker configuration', {
        id,
        userId,
        data: { ...data, apiSecret: '[REDACTED]', accessToken: '[REDACTED]' },
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Delete a broker configuration (soft delete)
   */
  async deleteBroker(id: number, userId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`Deleting broker configuration ${id} for user ${userId}`);

      // Verify broker exists and belongs to user
      const existingBroker = await this.brokerRepository.findByIdAndUserId(id, userId);
      if (!existingBroker) {
        throw new BrokerNotFoundError(id, userId);
      }

      await this.brokerRepository.delete(id);

      this.logger.log(`Successfully deleted broker with ID: ${id}`);
      return { message: `Broker configuration with ID ${id} has been deleted` };
    } catch (error) {
      this.logger.error('Failed to delete broker configuration', {
        id,
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get broker by ID for a specific user
   */
  async findBrokerById(id: number, userId: string): Promise<PublicBroker> {
    try {
      this.logger.log(`Finding broker ${id} for user ${userId}`);

      const broker = await this.brokerRepository.findByIdAndUserId(id, userId);
      if (!broker) {
        throw new BrokerNotFoundError(id, userId);
      }

      return this.toPublicBroker(broker);
    } catch (error) {
      this.logger.error('Failed to find broker', {
        id,
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get all brokers for a user with pagination
   */
  async findAllBrokers(filters: BrokerSearch, options?: PaginationOptions): Promise<PaginatedResult<PublicBroker>> {
    try {
      this.logger.log('Finding brokers with filters', { filters, options });

      const result = await this.brokerRepository.search(filters, options);

      return {
        ...result,
        data: result.data.map((broker) => this.toPublicBroker(broker)),
      };
    } catch (error) {
      this.logger.error('Failed to find brokers', {
        filters,
        options,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Update broker credentials with validation
   */
  async updateCredentials(
    id: number,
    userId: string,
    credentials: {
      apiKey?: string;
      apiSecret?: string;
      accessToken?: string;
      status?: BrokerStatus;
    },
  ): Promise<PublicBroker> {
    try {
      this.logger.log(`Updating credentials for broker ${id}`);

      const updatedBroker = await this.brokerRepository.updateCredentials(id, userId, credentials);

      this.logger.log(`Successfully updated credentials for broker ${id}`);
      return this.toPublicBroker(updatedBroker);
    } catch (error) {
      this.logger.error('Failed to update broker credentials', {
        id,
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get active brokers for a user
   */
  async getActiveBrokers(userId: string, options?: PaginationOptions): Promise<PaginatedResult<PublicBroker>> {
    try {
      this.logger.log(`Getting active brokers for user ${userId}`);

      const result = await this.brokerRepository.findActiveByUserId(userId, options);

      return {
        ...result,
        data: result.data.map((broker) => this.toPublicBroker(broker)),
      };
    } catch (error) {
      this.logger.error('Failed to get active brokers', {
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get broker status counts for a user
   */
  async getBrokerStatusCounts(userId: string): Promise<Record<BrokerStatus, number>> {
    try {
      this.logger.log(`Getting broker status counts for user ${userId}`);

      const counts = await this.brokerRepository.countByUserAndStatus(userId);

      this.logger.log(`Retrieved broker status counts for user ${userId}`, counts);
      return counts;
    } catch (error) {
      this.logger.error('Failed to get broker status counts', {
        userId,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== HELPER METHODS ====================

  /**
   * Check if broker type is supported
   */
  private isSupportedBrokerType(type: string): type is BrokerType {
    return type === 'ZERODHA_KITE';
  }

  /**
   * Get default settings for broker type
   */
  private getDefaultSettings(type: BrokerType): Record<string, unknown> {
    switch (type) {
      case 'ZERODHA_KITE':
        return {
          autoReconnect: true,
          maxRetryAttempts: 3,
          retryDelay: 1000,
          subscriptionMode: 'quote',
          enableOrderUpdates: true,
          enablePositionUpdates: true,
        };
      default:
        return {};
    }
  }

  /**
   * Convert broker entity to public broker (excluding sensitive fields)
   */
  private toPublicBroker(broker: BrokerTableSelect): PublicBroker {
    return {
      id: broker.id,
      name: broker.name,
      type: broker.type as BrokerType,
      userId: broker.userId,
      status: broker.status as BrokerStatus,
      isActive: broker.isActive,
      lastConnectedAt: broker.lastConnectedAt || undefined,
      lastErrorMessage: broker.lastErrorMessage || undefined,
      connectionAttempts: parseInt(broker.connectionAttempts || '0', 10),
      hasAccessToken: !!broker.accessToken,
      settings: broker.settings ? JSON.parse(broker.settings) : undefined,
      createdAt: broker.createdAt,
      createdBy: broker.createdBy,
      updatedAt: broker.updatedAt,
      updatedBy: broker.updatedBy,
      deletedAt: broker.deletedAt || undefined,
      deletedBy: broker.deletedBy || undefined,
    };
  }
}
