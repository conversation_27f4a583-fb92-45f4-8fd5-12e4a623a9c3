import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { BrokerAuthService } from './broker-auth.service';
import { DateTimeUtilsService } from '@app/utils';
import { EncryptionService } from '@app/common/encryption';
import { BrokerRepository } from './broker.repository';

describe('BrokerAuthService', () => {
  let service: BrokerAuthService;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockEncryptionService: jest.Mocked<EncryptionService>;
  let mockBrokerRepository: jest.Mocked<BrokerRepository>;

  beforeEach(async () => {
    // Create mocks
    mockDateTimeUtils = {
      getUtcNow: jest.fn(),
      addHours: jest.fn(),
      toUtcStorage: jest.fn(),
    } as any;

    mockEncryptionService = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    } as any;

    mockBrokerRepository = {
      findByUserIdAndType: jest.fn(),
      updateCredentials: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BrokerAuthService,
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: EncryptionService, useValue: mockEncryptionService },
        { provide: BrokerRepository, useValue: mockBrokerRepository },
      ],
    }).compile();

    service = module.get<BrokerAuthService>(BrokerAuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBrokerCredentials', () => {
    it('should retrieve and decrypt broker credentials', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockBroker = {
        id: 1,
        apiKey: 'test-api-key',
        apiSecret: 'encrypted-api-secret',
        accessToken: 'encrypted-access-token',
        type: 'ZERODHA_KITE',
      };

      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(mockBroker);
      mockEncryptionService.decrypt.mockReturnValue('decrypted-api-secret');

      // Act
      const result = await service.getBrokerCredentials({
        userId,
        brokerType: 'ZERODHA_KITE',
      });

      // Assert
      expect(result).toEqual({
        apiKey: 'test-api-key',
        apiSecret: 'decrypted-api-secret',
        brokerType: 'ZERODHA_KITE',
        hasAccessToken: true,
      });
      expect(mockBrokerRepository.findByUserIdAndType).toHaveBeenCalledWith(userId, 'ZERODHA_KITE');
      expect(mockEncryptionService.decrypt).toHaveBeenCalledWith('encrypted-api-secret');
    });

    it('should throw error when broker not found', async () => {
      // Arrange
      const userId = 'test-user-123';
      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getBrokerCredentials({
          userId,
          brokerType: 'ZERODHA_KITE',
        }),
      ).rejects.toThrow();
    });
  });

  describe('generateAccessToken', () => {
    it('should generate and store access token', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockBroker = {
        id: 1,
        userId,
        type: 'ZERODHA_KITE',
      };

      // Mock KiteConnect behavior (this would need proper mocking in real tests)
      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(mockBroker);
      mockEncryptionService.encrypt.mockReturnValue('encrypted-access-token');
      mockBrokerRepository.updateCredentials.mockResolvedValue(mockBroker as any);
      mockDateTimeUtils.getUtcNow.mockReturnValue('2024-01-01T10:00:00.000');
      mockDateTimeUtils.addHours.mockReturnValue('2024-01-02T10:00:00.000');
      mockDateTimeUtils.toUtcStorage.mockReturnValue('2024-01-01T10:00:00.000');

      // Note: This test would need proper KiteConnect mocking for full functionality
      // For now, we're just testing the structure
    });
  });

  describe('getStoredAccessToken', () => {
    it('should retrieve and decrypt stored access token', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockBroker = {
        id: 1,
        userId,
        name: 'Test Broker',
        accessToken: 'encrypted-access-token',
        lastConnectedAt: '2024-01-01T10:00:00.000',
      };

      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(mockBroker);
      mockEncryptionService.decrypt.mockReturnValue('decrypted-access-token');
      mockDateTimeUtils.getUtcNow.mockReturnValue('2024-01-01T12:00:00.000');
      mockDateTimeUtils.addHours.mockReturnValue('2024-01-02T10:00:00.000');
      mockDateTimeUtils.toUtcStorage.mockReturnValue('2024-01-02T10:00:00.000');

      // Act
      const result = await service.getStoredAccessToken({
        userId,
        brokerType: 'ZERODHA_KITE',
      });

      // Assert
      expect(result).toEqual({
        accessToken: 'decrypted-access-token',
        brokerUserId: userId,
        brokerUserName: 'Test Broker',
        email: undefined,
        loginTime: '2024-01-01T10:00:00.000',
        expiresAt: '2024-01-02T10:00:00.000',
        isValid: true,
      });
    });

    it('should return null when no stored token found', async () => {
      // Arrange
      const userId = 'test-user-123';
      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(null);

      // Act
      const result = await service.getStoredAccessToken({
        userId,
        brokerType: 'ZERODHA_KITE',
      });

      // Assert
      expect(result).toBeNull();
    });
  });
});
