import { Injectable, Lo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { Pool, PoolClient, QueryResult } from 'pg';
import { z } from 'zod/v4';
import { EnvService } from '../env/env.service';
import { HealthCheckService } from '../health-check/health-check.service';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import {
  QuestDBError,
  createQuestDBConnectionError,
  createQuestDBQueryError,
  createQuestDBConfigError,
} from './questdb.error';
import { QuestDBConnectionConfig, QuestDBConnectionConfigSchema, createQuestDBConfig } from './questdb-config.schema';
import { QuestDBTableSchema } from '@app/common/questdb';

/**
 * Query result interface for QuestDB operations
 */
export interface QuestDBQueryResult<T = Record<string, unknown>> {
  data: T[];
  rowCount: number;
  executedAt: string;
  executionTimeMs: number;
}

/**
 * Connection statistics interface
 */
export interface QuestDBConnectionStats {
  isConnected: boolean;
  totalConnections: number;
  idleConnections: number;
  waitingCount: number;
  connectionAttempts: number;
  lastHealthCheck: string | null;
  healthCheckCount: number;
  failedHealthChecks: number;
}

/**
 * QuestDB Service
 *
 * Provides connection management and basic query execution for QuestDB
 * via PostgreSQL wire protocol. Follows the same architectural patterns
 * as DrizzleService for consistency across the codebase.
 *
 * This service focuses on:
 * - Connection pooling and management
 * - Automatic reconnection with exponential backoff
 * - Health monitoring and status reporting
 * - Basic query execution with error handling
 * - Integration with existing core services
 *
 * Note: Database operations (CRUD, time-series, batch) are now handled
 * by repository classes that extend BaseQuestDBRepository.
 */
@Injectable()
export class QuestDBService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(QuestDBService.name);

  // Connection management
  private connection: Pool | null = null;
  private isConnected = false;

  // Resilience properties
  private connectionAttempts = 0;
  private readonly maxRetryAttempts = 5;
  private readonly retryDelay = 5000; // 5 seconds
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private reconnectionTimeout: NodeJS.Timeout | null = null;

  // Health check properties
  private lastHealthCheck: Date | null = null;
  private healthCheckCount = 0;
  private failedHealthChecks = 0;

  // Table schema management
  private registeredTableSchemas: Map<string, QuestDBTableSchema> = new Map();
  private tablesInitialized = false;

  constructor(
    private readonly envService: EnvService,
    private readonly healthCheckService: HealthCheckService,
    private readonly datetimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Initialize the QuestDB service
   * Called automatically by NestJS when the module starts
   */
  async onModuleInit(): Promise<void> {
    this.logger.log('Initializing QuestDB service...');
    await this.connectWithRetry();
    this.startHealthCheck();
    await this.initializeTables();
  }

  /**
   * Cleanup when the module is destroyed
   * Called automatically by NestJS when the module shuts down
   */
  async onModuleDestroy(): Promise<void> {
    this.logger.log('Shutting down QuestDB service...');
    await this.disconnect();
  }

  /**
   * Get the QuestDB connection pool
   * @throws {QuestDBError} If connection is not established
   */
  getConnection(): Pool {
    if (!this.connection || !this.isConnected) {
      throw new QuestDBError('CONNECTION_FAILED', {
        message: 'QuestDB not connected. Please ensure the service is initialized.',
      });
    }
    return this.connection;
  }

  /**
   * Check if the connection is healthy
   */
  isConnectionHealthy(): boolean {
    return this.isConnected && this.connection !== null;
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): QuestDBConnectionStats {
    return {
      isConnected: this.isConnected,
      totalConnections: this.connection?.totalCount || 0,
      idleConnections: this.connection?.idleCount || 0,
      waitingCount: this.connection?.waitingCount || 0,
      connectionAttempts: this.connectionAttempts,
      lastHealthCheck: this.lastHealthCheck ? this.lastHealthCheck.toISOString() : null,
      healthCheckCount: this.healthCheckCount,
      failedHealthChecks: this.failedHealthChecks,
    };
  }

  /**
   * Execute a SQL query against QuestDB
   * @param query SQL query string
   * @param params Query parameters for parameterized queries
   * @returns Query result with data, metadata, and execution statistics
   */
  async executeQuery<T = Record<string, unknown>>(query: string, params?: unknown[]): Promise<QuestDBQueryResult<T>> {
    if (!this.connection || !this.isConnected) {
      throw new QuestDBError('CONNECTION_FAILED', {
        message: 'QuestDB not connected. Please ensure the service is initialized.',
      });
    }

    const startTime = this.datetimeUtils.getTime();
    let client: PoolClient | null = null;

    try {
      // Get client from pool
      client = await this.connection.connect();

      // Execute query with parameters
      const result: QueryResult =
        params && params.length > 0 ? await client.query(query, params) : await client.query(query);

      const executionTime = this.datetimeUtils.getTime() - startTime;

      this.logger.debug('Query executed successfully', {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        executionTime,
        rowCount: result.rowCount || 0,
      });

      return {
        data: result.rows as T[],
        rowCount: result.rowCount || 0,
        executedAt: this.datetimeUtils.getUtcNow(),
        executionTimeMs: executionTime,
      };
    } catch (error) {
      const executionTime = this.datetimeUtils.getTime() - startTime;

      this.logger.error(`Query execution failed: ${this.errorUtils.getErrorMessage(error)}`, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        executionTime,
        error: this.errorUtils.getErrorStack(error),
      });

      throw createQuestDBQueryError(query, error);
    } finally {
      // Always release the client back to the pool
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Execute a query with timeout
   * @param query SQL query string
   * @param params Query parameters
   * @param timeoutMs Timeout in milliseconds (default: 30000)
   */
  async executeQueryWithTimeout<T = Record<string, unknown>>(
    query: string,
    params?: unknown[],
    timeoutMs: number = 30000,
  ): Promise<QuestDBQueryResult<T>> {
    return Promise.race([
      this.executeQuery<T>(query, params),
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(
            new QuestDBError('CONNECTION_TIMEOUT', {
              message: `Query execution timed out after ${timeoutMs}ms`,
            }),
          );
        }, timeoutMs);
      }),
    ]);
  }

  /**
   * Execute multiple queries in a transaction
   * @param queries Array of query objects with query string and optional parameters
   */
  async executeTransaction(queries: Array<{ query: string; params?: unknown[] }>): Promise<QuestDBQueryResult[]> {
    if (!this.connection || !this.isConnected) {
      throw new QuestDBError('CONNECTION_FAILED', {
        message: 'QuestDB not connected. Please ensure the service is initialized.',
      });
    }

    const client = await this.connection.connect();
    const results: QuestDBQueryResult[] = [];

    try {
      await client.query('BEGIN');

      for (const { query, params } of queries) {
        const startTime = this.datetimeUtils.getTime();
        const result = params && params.length > 0 ? await client.query(query, params) : await client.query(query);

        results.push({
          data: result.rows,
          rowCount: result.rowCount || 0,
          executedAt: this.datetimeUtils.getUtcNow(),
          executionTimeMs: this.datetimeUtils.getTime() - startTime,
        });
      }

      await client.query('COMMIT');
      this.logger.debug(`Transaction completed successfully with ${queries.length} queries`);

      return results;
    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error(`Transaction failed: ${this.errorUtils.getErrorMessage(error)}`, {
        error: this.errorUtils.getErrorStack(error),
      });

      throw new QuestDBError('TRANSACTION_FAILED', {
        message: `Transaction failed: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    } finally {
      client.release();
    }
  }

  /**
   * Connect to QuestDB with retry logic
   */
  private async connectWithRetry(): Promise<void> {
    while (this.connectionAttempts < this.maxRetryAttempts) {
      try {
        await this.connect();
        this.connectionAttempts = 0; // Reset on successful connection
        return;
      } catch (error) {
        this.connectionAttempts++;
        this.logger.error(
          `Connection attempt ${this.connectionAttempts}/${this.maxRetryAttempts} failed: ${this.errorUtils.getErrorMessage(error)}`,
          { error: this.errorUtils.getErrorStack(error) },
        );

        if (this.connectionAttempts >= this.maxRetryAttempts) {
          throw createQuestDBConnectionError(error, 'Max connection attempts exceeded');
        }

        // Exponential backoff
        const delay = this.retryDelay * Math.pow(2, this.connectionAttempts - 1);
        this.logger.log(`Retrying connection in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      return; // Already started
    }

    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck().catch((error) => {
        this.logger.error(`Health check error: ${this.errorUtils.getErrorMessage(error)}`);
      });
    }, 30000); // Every 30 seconds

    this.logger.debug('Health check monitoring started');
  }

  /**
   * Establish connection to QuestDB
   */
  private async connect(): Promise<void> {
    try {
      // Get connection configuration from environment
      const config = this.getConnectionConfig();

      // Validate configuration
      const validatedConfig = QuestDBConnectionConfigSchema.parse(config);

      this.logger.log('Establishing QuestDB connection...', {
        host: validatedConfig.host,
        port: validatedConfig.port,
        database: validatedConfig.database,
        username: validatedConfig.username,
      });

      // Create connection pool
      this.connection = new Pool({
        host: validatedConfig.host,
        port: validatedConfig.port,
        database: validatedConfig.database,
        user: validatedConfig.username,
        password: validatedConfig.password,
        ssl: validatedConfig.ssl,
        max: validatedConfig.maxConnections,
        connectionTimeoutMillis: validatedConfig.connectionTimeoutMillis,
        idleTimeoutMillis: validatedConfig.idleTimeoutMillis,
      });

      // Test the connection
      await this.testConnection();

      this.isConnected = true;
      this.healthCheckService.setQuestdbStatus(true, this.getConnectionStatsForHealthCheck());
      this.logger.log('QuestDB connection established successfully');
    } catch (error) {
      this.isConnected = false;
      this.healthCheckService.setQuestdbStatus(false);

      if (error instanceof z.ZodError) {
        throw createQuestDBConfigError('connection configuration', error);
      }

      throw createQuestDBConnectionError(error);
    }
  }

  /**
   * Get connection configuration from environment variables
   */
  private getConnectionConfig(): QuestDBConnectionConfig {
    return createQuestDBConfig({
      QUESTDB_HOST: this.envService.get('QUESTDB_HOST'),
      QUESTDB_PORT: this.envService.get('QUESTDB_PORT'),
      QUESTDB_DATABASE: this.envService.get('QUESTDB_DATABASE'),
      QUESTDB_USERNAME: this.envService.get('QUESTDB_USERNAME'),
      QUESTDB_PASSWORD: this.envService.get('QUESTDB_PASSWORD'),
      QUESTDB_SSL: this.envService.get('QUESTDB_SSL'),
      QUESTDB_MAX_CONNECTIONS: parseInt(this.envService.getArbitrary('QUESTDB_MAX_CONNECTIONS', '10') || '10', 10),
    });
  }

  /**
   * Test the database connection
   */
  private async testConnection(): Promise<boolean> {
    if (!this.connection) {
      return false;
    }

    try {
      const client = await this.connection.connect();
      await client.query('SELECT 1 as connection_test');
      client.release();
      return true;
    } catch (error) {
      this.logger.error(`Connection test failed: ${this.errorUtils.getErrorMessage(error)}`);
      return false;
    }
  }

  /**
   * Disconnect from QuestDB
   */
  private async disconnect(): Promise<void> {
    try {
      // Clear intervals
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      if (this.reconnectionTimeout) {
        clearTimeout(this.reconnectionTimeout);
        this.reconnectionTimeout = null;
      }

      // Close connection pool
      if (this.connection) {
        await this.connection.end();
        this.connection = null;
      }

      this.isConnected = false;
      this.healthCheckService.setQuestdbStatus(false);
      this.logger.log('QuestDB connection closed successfully');
    } catch (error) {
      this.logger.error(`Error during disconnect: ${this.errorUtils.getErrorMessage(error)}`);
    }
  }

  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      this.lastHealthCheck = this.datetimeUtils.getNewDate();
      this.healthCheckCount++;

      const isHealthy = await this.testConnection();

      if (!isHealthy && this.isConnected) {
        this.logger.warn('Health check failed, marking connection as unhealthy');
        this.handleConnectionError(new Error('Health check failed'));
      } else if (isHealthy && !this.isConnected) {
        this.logger.log('Health check passed, connection restored');
        this.isConnected = true;
        this.failedHealthChecks = 0;
      }

      if (isHealthy) {
        this.failedHealthChecks = 0;
        this.healthCheckService.setQuestdbStatus(true, this.getConnectionStatsForHealthCheck());
      } else {
        this.failedHealthChecks++;
        this.healthCheckService.setQuestdbStatus(false);
      }
    } catch (error) {
      this.logger.error(`Health check error: ${this.errorUtils.getErrorMessage(error)}`);
      this.handleConnectionError(this.errorUtils.toError(error));
    }
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(error: Error): void {
    this.logger.error(`Connection error detected: ${this.errorUtils.getErrorMessage(error)}`, {
      error: this.errorUtils.getErrorStack(error),
    });

    this.isConnected = false;
    this.failedHealthChecks++;
    this.healthCheckService.setQuestdbStatus(false);

    // Schedule reconnection if not already scheduled
    this.scheduleReconnection();
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnection(): void {
    if (this.reconnectionTimeout) {
      return; // Already scheduled
    }

    this.reconnectionTimeout = setTimeout(() => {
      this.performReconnection()
        .catch((error) => {
          this.logger.error(`Reconnection failed: ${this.errorUtils.getErrorMessage(error)}`);
        })
        .finally(() => {
          this.reconnectionTimeout = null;
        });
    }, this.retryDelay);
  }

  /**
   * Perform reconnection
   */
  private async performReconnection(): Promise<void> {
    this.logger.log('Attempting to reconnect to QuestDB...');

    try {
      await this.connectWithRetry();
      this.logger.log('Reconnection successful');
    } catch (error) {
      this.logger.error(`Reconnection failed: ${this.errorUtils.getErrorMessage(error)}`);
      // Schedule another reconnection attempt
      this.scheduleReconnection();
    }
  }

  /**
   * Get connection statistics formatted for health check service
   */
  private getConnectionStatsForHealthCheck() {
    return {
      totalConnections: this.connection?.totalCount || 0,
      idleConnections: this.connection?.idleCount || 0,
      waitingCount: this.connection?.waitingCount || 0,
      healthCheckCount: this.healthCheckCount,
      failedHealthChecks: this.failedHealthChecks,
    };
  }

  // ==================== TABLE SCHEMA MANAGEMENT ====================

  /**
   * Register a table schema for automatic initialization
   * This method should be called by feature modules during their initialization
   */
  registerTableSchema(schema: QuestDBTableSchema): void {
    this.registeredTableSchemas.set(schema.tableName, schema);
    this.logger.debug(`Registered table schema: ${schema.tableName}`);

    // If tables have already been initialized and connection is ready, initialize this table immediately
    if (this.tablesInitialized && this.isConnected) {
      this.initializeTable(schema).catch((error) => {
        this.logger.error(`Failed to initialize table ${schema.tableName}`, {
          error: this.errorUtils.getErrorMessage(error),
        });
      });
    }
  }

  /**
   * Initialize all registered tables
   * Called automatically during service initialization
   */
  private async initializeTables(): Promise<void> {
    if (this.tablesInitialized) {
      return;
    }

    this.logger.log('Initializing QuestDB tables...');

    const tableCount = this.registeredTableSchemas.size;
    if (tableCount === 0) {
      this.logger.log('No table schemas registered for initialization');
      this.tablesInitialized = true;
      return;
    }

    let successCount = 0;
    let failureCount = 0;

    for (const [tableName, schema] of this.registeredTableSchemas) {
      try {
        await this.initializeTable(schema);
        successCount++;
        this.logger.log(`Successfully initialized table: ${tableName}`);
      } catch (error) {
        failureCount++;
        this.logger.error(`Failed to initialize table: ${tableName}`, {
          error: this.errorUtils.getErrorMessage(error),
          stack: this.errorUtils.getErrorStack(error),
        });
      }
    }

    this.tablesInitialized = true;
    this.logger.log(`Table initialization completed`, {
      totalTables: tableCount,
      successful: successCount,
      failed: failureCount,
    });
  }

  /**
   * Initialize a single table with its schema
   */
  private async initializeTable(schema: QuestDBTableSchema): Promise<void> {
    try {
      // Create the main table
      await this.executeQuery(schema.createTableSQL);
      this.logger.debug(`Created table: ${schema.tableName}`);

      // Create indexes if provided
      if (schema.createIndexesSQL && schema.createIndexesSQL.length > 0) {
        for (const indexSQL of schema.createIndexesSQL) {
          try {
            await this.executeQuery(indexSQL);
          } catch (error) {
            // Indexes might already exist, log warning but continue
            this.logger.warn(`Index creation warning for table ${schema.tableName}`, {
              error: this.errorUtils.getErrorMessage(error),
              indexSQL: indexSQL.substring(0, 100),
            });
          }
        }
        this.logger.debug(`Created indexes for table: ${schema.tableName}`);
      }
    } catch (error) {
      throw new QuestDBError('SCHEMA_VALIDATION_FAILED', {
        message: `Failed to initialize table ${schema.tableName}: ${this.errorUtils.getErrorMessage(error)}`,
        cause: error,
      });
    }
  }

  /**
   * Get all registered table schemas
   */
  getRegisteredTableSchemas(): Map<string, QuestDBTableSchema> {
    return new Map(this.registeredTableSchemas);
  }

  /**
   * Check if tables have been initialized
   */
  areTablesInitialized(): boolean {
    return this.tablesInitialized;
  }
}
