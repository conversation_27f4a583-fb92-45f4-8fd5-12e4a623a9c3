/**
 * Symbol QuestDB Model
 *
 * Defines comprehensive Zod schemas for symbol master data stored in QuestDB.
 * This model provides type-safe data validation and transformation between
 * database formats (snake_case) and TypeScript formats (camelCase).
 *
 * Features:
 * - Zod schemas for data validation and transformation
 * - QuestDB-specific field type validation
 * - Automatic conversion between snake_case and camelCase
 * - Type-safe operations with schema-derived types
 * - Centralized data structure definition
 * - Enhanced error handling with Zod validation
 */

import { z } from 'zod/v4';
import { utcDateTimeSchema } from '@app/common/schema';
import type { QuestDBTableSchema } from '@app/common/questdb';
import { KiteInstrumentTypeEnum, KiteExchangeEnum, KiteSegmentEnum } from './symbol.schema';

/**
 * QuestDB table name for symbol master data
 */
export const SYMBOL_TABLE = 'symbol';

// ==================== QUESTDB ZODE SCHEMAS ====================

/**
 * Raw QuestDB symbol data schema (snake_case columns as stored in database)
 * Matches the exact structure of data returned from QuestDB queries
 */
export const SymbolQuestDBRawSchema = z.object({
  // Primary identifiers
  instrument_token: z.string().min(1).describe('Kite instrument token (primary identifier)'),
  exchange_token: z.string().min(1).describe('Exchange token'),

  // Symbol information
  trading_symbol: z.string().min(1).max(50).describe('Trading symbol'),
  name: z.string().max(255).describe('Instrument name'),

  // Market data
  last_price: z.number().nonnegative().describe('Last traded price'),
  tick_size: z.number().positive().describe('Minimum price movement'),
  lot_size: z.number().int().positive().describe('Lot size'),

  // Derivatives specific (optional)
  expiry: utcDateTimeSchema.optional().describe('Expiry timestamp for derivatives'),
  strike: z.number().nonnegative().optional().describe('Strike price for options'),

  // Classification
  instrument_type: z.string().min(1).describe('Type of instrument'),
  segment: z.string().min(1).describe('Market segment'),
  exchange: z.string().min(1).describe('Exchange'),

  // Status and metadata
  is_active: z.boolean().default(true).describe('Whether instrument is active'),
  downloaded_at: utcDateTimeSchema.describe('When this data was downloaded'),
  updated_at: utcDateTimeSchema.describe('Last update timestamp'),
  timestamp: utcDateTimeSchema.describe('Designated timestamp column'),
});

export type SymbolQuestDBRaw = z.output<typeof SymbolQuestDBRawSchema>;

/**
 * Symbol entity schema (camelCase for TypeScript usage)
 * Transformed version of QuestDB raw data with proper TypeScript types
 */
export const SymbolQuestDBEntitySchema = SymbolQuestDBRawSchema.transform((raw) => ({
  // Primary identifiers
  instrumentToken: raw.instrument_token,
  exchangeToken: raw.exchange_token,

  // Symbol information
  tradingSymbol: raw.trading_symbol,
  name: raw.name,

  // Market data
  lastPrice: raw.last_price,
  tickSize: raw.tick_size,
  lotSize: raw.lot_size,

  // Derivatives specific
  expiry: raw.expiry ? new Date(raw.expiry) : undefined,
  strike: raw.strike,

  // Classification (with enum validation)
  instrumentType: raw.instrument_type,
  segment: raw.segment,
  exchange: raw.exchange,

  // Status and metadata
  isActive: raw.is_active,
  downloadedAt: raw.downloaded_at,
  updatedAt: raw.updated_at,
  timestamp: raw.timestamp,
}));

export type SymbolEntity = z.output<typeof SymbolQuestDBEntitySchema>;

/**
 * Symbol create data schema (for insert operations)
 * Excludes auto-generated timestamp fields
 */
export const SymbolQuestDBCreateSchema = z.object({
  // Primary identifiers
  instrumentToken: z.string().min(1).describe('Kite instrument token'),
  exchangeToken: z.string().min(1).describe('Exchange token'),

  // Symbol information
  tradingSymbol: z.string().min(1).max(50).describe('Trading symbol'),
  name: z.string().max(255).describe('Instrument name'),

  // Market data
  lastPrice: z.number().nonnegative().describe('Last traded price'),
  tickSize: z.number().positive().describe('Minimum price movement'),
  lotSize: z.number().int().positive().describe('Lot size'),

  // Derivatives specific (optional)
  expiry: z.date().optional().describe('Expiry date for derivatives'),
  strike: z.number().nonnegative().optional().describe('Strike price for options'),

  // Classification (with enum validation)
  instrumentType: KiteInstrumentTypeEnum.describe('Type of instrument'),
  segment: KiteSegmentEnum.describe('Market segment'),
  exchange: KiteExchangeEnum.describe('Exchange'),

  // Status
  isActive: z.boolean().default(true).describe('Whether instrument is active'),
});

export type SymbolCreateData = z.output<typeof SymbolQuestDBCreateSchema>;

/**
 * Symbol update data schema (for update operations)
 * All fields optional except identifiers
 */
export const SymbolQuestDBUpdateSchema = z.object({
  // Symbol information
  tradingSymbol: z.string().min(1).max(50).optional().describe('Trading symbol'),
  name: z.string().max(255).optional().describe('Instrument name'),

  // Market data
  lastPrice: z.number().nonnegative().optional().describe('Last traded price'),
  tickSize: z.number().positive().optional().describe('Minimum price movement'),
  lotSize: z.number().int().positive().optional().describe('Lot size'),

  // Derivatives specific
  expiry: z.date().optional().describe('Expiry date for derivatives'),
  strike: z.number().nonnegative().optional().describe('Strike price for options'),

  // Classification
  instrumentType: KiteInstrumentTypeEnum.optional().describe('Type of instrument'),
  segment: KiteSegmentEnum.optional().describe('Market segment'),
  exchange: KiteExchangeEnum.optional().describe('Exchange'),

  // Status
  isActive: z.boolean().optional().describe('Whether instrument is active'),
});

export type SymbolUpdateData = z.output<typeof SymbolQuestDBUpdateSchema>;

// ==================== TRANSFORMATION UTILITIES ====================

/**
 * Parse raw QuestDB query result into validated SymbolEntity
 * Handles snake_case to camelCase conversion and type validation
 */
export function parseQuestDBRaw(raw: unknown): SymbolEntity {
  const validatedRaw = SymbolQuestDBRawSchema.parse(raw);
  return SymbolQuestDBEntitySchema.parse(validatedRaw);
}

/**
 * Transform SymbolCreateData to QuestDB format (camelCase to snake_case)
 * Includes timestamp generation for database operations
 */
export function transformToQuestDBFormat(
  data: SymbolCreateData,
  timestamps: {
    downloadedAt: string;
    updatedAt: string;
    timestamp: string;
  },
): Record<string, unknown> {
  return {
    // Primary identifiers
    instrument_token: data.instrumentToken,
    exchange_token: data.exchangeToken,

    // Symbol information
    trading_symbol: data.tradingSymbol,
    name: data.name,

    // Market data
    last_price: data.lastPrice,
    tick_size: data.tickSize,
    lot_size: data.lotSize,

    // Derivatives specific
    expiry: data.expiry?.toISOString(),
    strike: data.strike,

    // Classification
    instrument_type: data.instrumentType,
    segment: data.segment,
    exchange: data.exchange,

    // Status and metadata
    is_active: data.isActive,
    downloaded_at: timestamps.downloadedAt,
    updated_at: timestamps.updatedAt,
    timestamp: timestamps.timestamp,
  };
}

/**
 * Transform SymbolUpdateData to QuestDB format (camelCase to snake_case)
 * Only includes fields that are being updated
 */
export function transformUpdateToQuestDBFormat(data: SymbolUpdateData, updatedAt: string): Record<string, unknown> {
  const result: Record<string, unknown> = {
    updated_at: updatedAt,
  };

  // Only include fields that are defined
  if (data.tradingSymbol !== undefined) result.trading_symbol = data.tradingSymbol;
  if (data.name !== undefined) result.name = data.name;
  if (data.lastPrice !== undefined) result.last_price = data.lastPrice;
  if (data.tickSize !== undefined) result.tick_size = data.tickSize;
  if (data.lotSize !== undefined) result.lot_size = data.lotSize;
  if (data.expiry !== undefined) result.expiry = data.expiry?.toISOString();
  if (data.strike !== undefined) result.strike = data.strike;
  if (data.instrumentType !== undefined) result.instrument_type = data.instrumentType;
  if (data.segment !== undefined) result.segment = data.segment;
  if (data.exchange !== undefined) result.exchange = data.exchange;
  if (data.isActive !== undefined) result.is_active = data.isActive;

  return result;
}

/**
 * QuestDB table creation SQL for symbol master data
 */
export const CREATE_SYMBOL_TABLE_SQL = `
CREATE TABLE IF NOT EXISTS ${SYMBOL_TABLE} (
    -- Primary identifiers
    instrument_token STRING NOT NULL,
    exchange_token STRING NOT NULL,
    
    -- Symbol information
    trading_symbol STRING NOT NULL,
    name STRING,
    
    -- Market data
    last_price DOUBLE,
    tick_size DOUBLE NOT NULL,
    lot_size INT NOT NULL,
    
    -- Derivatives specific
    expiry TIMESTAMP,
    strike DOUBLE,
    
    -- Classification
    instrument_type STRING NOT NULL,
    segment STRING NOT NULL,
    exchange STRING NOT NULL,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    downloaded_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    timestamp TIMESTAMP NOT NULL
) timestamp(timestamp) PARTITION BY DAY;
`;

/**
 * QuestDB indexes for optimized queries
 */
export const CREATE_SYMBOL_INDEXES_SQL = [
  // Primary index on instrument_token for fast lookups
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_instrument_token (instrument_token);`,

  // Index on trading_symbol for symbol searches
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_trading_symbol (trading_symbol);`,

  // Composite index for common query patterns
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_exchange_segment (exchange, segment);`,

  // Index on is_active for filtering active symbols
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_is_active (is_active);`,
];

/**
 * Common SQL query templates for symbol operations
 */
export const SYMBOL_QUERIES = {
  // Upsert symbol data with deduplication
  UPSERT_SYMBOL: `
    INSERT INTO ${SYMBOL_TABLE} (
      instrument_token, exchange_token, trading_symbol, name,
      last_price, tick_size, lot_size, expiry, strike,
      instrument_type, segment, exchange, is_active,
      downloaded_at, updated_at, timestamp
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
    )
    ON CONFLICT (instrument_token, timestamp)
    DO UPDATE SET
      exchange_token = EXCLUDED.exchange_token,
      trading_symbol = EXCLUDED.trading_symbol,
      name = EXCLUDED.name,
      last_price = EXCLUDED.last_price,
      tick_size = EXCLUDED.tick_size,
      lot_size = EXCLUDED.lot_size,
      expiry = EXCLUDED.expiry,
      strike = EXCLUDED.strike,
      instrument_type = EXCLUDED.instrument_type,
      segment = EXCLUDED.segment,
      exchange = EXCLUDED.exchange,
      is_active = EXCLUDED.is_active,
      updated_at = EXCLUDED.updated_at
  `,

  // Get latest symbols by filters
  GET_LATEST_SYMBOLS: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
  `,

  // Get symbol by instrument token (latest version)
  GET_SYMBOL_BY_TOKEN: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE instrument_token = $1
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = $1
    )
    LIMIT 1
  `,

  // Get active symbols count
  GET_ACTIVE_SYMBOLS_COUNT: `
    SELECT COUNT(*) as count
    FROM ${SYMBOL_TABLE}
    WHERE is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
  `,

  // Get symbols by exchange (latest versions only)
  GET_SYMBOLS_BY_EXCHANGE: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE exchange = $1
    AND is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
    ORDER BY trading_symbol
  `,

  // Search symbols by trading symbol or name
  SEARCH_SYMBOLS: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE (trading_symbol ILIKE $1 OR name ILIKE $1)
    AND is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
    ORDER BY trading_symbol
    LIMIT $2 OFFSET $3
  `,
};

/**
 * Symbol QuestDB table schema definition
 * Used by the QuestDB service for automatic table initialization
 */
export const SYMBOL_QUESTDB_SCHEMA: QuestDBTableSchema = {
  tableName: SYMBOL_TABLE,
  createTableSQL: CREATE_SYMBOL_TABLE_SQL,
  createIndexesSQL: CREATE_SYMBOL_INDEXES_SQL,
  queries: SYMBOL_QUERIES,
  designatedTimestamp: 'timestamp',
  partitionBy: 'DAY',
};
