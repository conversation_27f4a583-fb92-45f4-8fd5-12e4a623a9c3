import { Test, TestingModule } from '@nestjs/testing';
import { SymbolService } from './symbol.service';
import { EnvService } from '@app/core/env';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';

// Mock dependencies
const mockEnvService = {
  get: jest.fn().mockImplementation((key: string) => {
    switch (key) {
      case 'QUESTDB_HOST':
        return 'localhost';
      case 'QUESTDB_ILP_PORT':
        return 9009;
      default:
        return undefined;
    }
  }),
};

const mockDateTimeUtils = {
  getTime: jest.fn().mockReturnValue(Date.now()),
  getUtcNow: jest.fn().mockReturnValue('2024-01-01T00:00:00.000Z'),
};

const mockErrorUtils = {
  getErrorMessage: jest.fn().mockImplementation((error: unknown) => {
    if (error instanceof Error) {
      return error.message;
    }
    return String(error);
  }),
};

describe('SymbolService', () => {
  let service: SymbolService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SymbolService,
        { provide: EnvService, useValue: mockEnvService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: ErrorUtilsService, useValue: mockErrorUtils },
      ],
    }).compile();

    service = module.get<SymbolService>(SymbolService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getHealthStatus', () => {
    it('should return healthy status when API is available', async () => {
      // Mock successful fetch
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200,
      });

      const result = await service.getHealthStatus();

      expect(result.isHealthy).toBe(true);
      expect(result.apiStatus).toBe('available');
      expect(result.lastFetch).toBeInstanceOf(Date);
    });

    it('should return unhealthy status when API is unavailable', async () => {
      // Mock failed fetch
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const result = await service.getHealthStatus();

      expect(result.isHealthy).toBe(false);
      expect(result.apiStatus).toBe('unavailable');
      expect(result.error).toBe('Network error');
    });
  });

  describe('mapRowToInstrument', () => {
    it('should correctly map CSV row to instrument object', () => {
      const csvRow = {
        instrument_token: '123456',
        exchange_token: '654321',
        tradingsymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        last_price: '2500.50',
        expiry: '2024-12-31',
        strike: '2600.00',
        tick_size: '0.05',
        lot_size: '1',
        instrument_type: 'EQ',
        segment: 'NSE',
        exchange: 'NSE',
      };

      // Access private method for testing
      const result = (service as any).mapRowToInstrument(csvRow);

      expect(result).toEqual({
        instrument_token: '123456',
        exchange_token: '654321',
        tradingsymbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        last_price: 2500.5,
        expiry: '2024-12-31',
        strike: 2600.0,
        tick_size: 0.05,
        lot_size: 1,
        instrument_type: 'EQ',
        segment: 'NSE',
        exchange: 'NSE',
      });
    });

    it('should handle empty and null values correctly', () => {
      const csvRow = {
        instrument_token: '123456',
        exchange_token: '654321',
        tradingsymbol: 'RELIANCE',
        name: '',
        last_price: '',
        expiry: '',
        strike: '',
        tick_size: '',
        lot_size: '',
        instrument_type: 'EQ',
        segment: 'NSE',
        exchange: 'NSE',
      };

      const result = (service as any).mapRowToInstrument(csvRow);

      expect(result).toEqual({
        instrument_token: '123456',
        exchange_token: '654321',
        tradingsymbol: 'RELIANCE',
        name: '',
        last_price: 0,
        expiry: null,
        strike: null,
        tick_size: 0,
        lot_size: 0,
        instrument_type: 'EQ',
        segment: 'NSE',
        exchange: 'NSE',
      });
    });
  });
});
