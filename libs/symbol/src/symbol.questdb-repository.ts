/**
 * Symbol Repository
 *
 * Simplified QuestDB repository for symbol master data operations.
 * Acts as a thin validation layer over QuestDB operations without data transformation.
 *
 * Features:
 * - Input validation using Zod schemas before database operations
 * - Output validation using Zod schemas after database retrieval
 * - Direct QuestDB operations without complex abstractions
 * - Type safety through exported TypeScript types from Zod schemas
 */

import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { QuestDBService } from '@app/core/questdb';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import {
  Symbol,
  SymbolUpsert,
  SymbolQueryFilters,
  SymbolUpsertSchema,
  SymbolQueryFiltersSchema,
  SymbolSchema,
} from './symbol.schema';
import { SYMBOL_QUESTDB_SCHEMA, SYMBOL_QUERIES, SymbolQuestDBRawSchema } from './symbol.questdb-model';

/**
 * Simplified Symbol Repository
 *
 * Clean, simple repository that acts as a thin validation layer over QuestDB operations.
 * Focuses on validation at boundaries while keeping business logic minimal.
 */
@Injectable()
export class SymbolRepository implements OnModuleInit {
  private readonly logger = new Logger(SymbolRepository.name);

  constructor(
    private readonly questdbService: QuestDBService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Initialize the repository and register table schema
   */
  onModuleInit(): void {
    this.questdbService.registerTableSchema(SYMBOL_QUESTDB_SCHEMA);
    this.logger.log('Symbol QuestDB repository initialized and table schema registered');
  }

  /**
   * Execute QuestDB query with error handling
   */
  private async executeQuery<T = Record<string, unknown>>(
    query: string,
    params: unknown[] = [],
  ): Promise<{ data: T[]; rowCount: number }> {
    try {
      const result = await this.questdbService.executeQuery<T>(query, params);
      return {
        data: result.data,
        rowCount: result.rowCount,
      };
    } catch (error) {
      this.logger.error('QuestDB query failed', {
        query: query.substring(0, 100),
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Validate and parse raw QuestDB result to Symbol
   */
  private parseRawToSymbol(raw: Record<string, unknown>): Symbol {
    // Validate raw database result
    const validatedRaw = SymbolQuestDBRawSchema.parse(raw);

    // Convert to Symbol format with validation
    return SymbolSchema.parse({
      instrumentToken: validatedRaw.instrument_token,
      exchangeToken: validatedRaw.exchange_token,
      tradingSymbol: validatedRaw.trading_symbol,
      name: validatedRaw.name,
      lastPrice: validatedRaw.last_price,
      tickSize: validatedRaw.tick_size,
      lotSize: validatedRaw.lot_size,
      expiry: validatedRaw.expiry,
      strike: validatedRaw.strike,
      instrumentType: validatedRaw.instrument_type,
      segment: validatedRaw.segment,
      exchange: validatedRaw.exchange,
      isActive: validatedRaw.is_active,
      downloadedAt: validatedRaw.downloaded_at,
      updatedAt: validatedRaw.updated_at,
    });
  }

  // ==================== SYMBOL OPERATIONS ====================

  /**
   * Upsert a single symbol record with validation
   */
  async upsertSymbol(symbolData: SymbolUpsert): Promise<{ success: boolean; isNew: boolean }> {
    try {
      // Validate input data
      const validatedData = SymbolUpsertSchema.parse(symbolData);

      this.logger.debug('Upserting symbol', {
        instrumentToken: validatedData.instrumentToken,
        tradingSymbol: validatedData.tradingSymbol,
      });

      // Check if symbol exists
      const existing = await this.findByInstrumentToken(validatedData.instrumentToken);
      const isNew = !existing;

      // Prepare data for QuestDB insert
      const now = this.dateTimeUtils.getUtcNow();
      const values = [
        validatedData.instrumentToken,
        validatedData.exchangeToken,
        validatedData.tradingSymbol,
        validatedData.name,
        validatedData.lastPrice,
        validatedData.tickSize,
        validatedData.lotSize,
        validatedData.expiry,
        validatedData.strike,
        validatedData.instrumentType,
        validatedData.segment,
        validatedData.exchange,
        validatedData.isActive,
        now, // downloaded_at
        now, // updated_at
        now, // timestamp
      ];

      await this.executeQuery(SYMBOL_QUERIES.UPSERT_SYMBOL, values);

      this.logger.debug('Symbol upserted successfully', {
        instrumentToken: validatedData.instrumentToken,
        isNew,
      });

      return { success: true, isNew };
    } catch (error) {
      this.logger.error('Failed to upsert symbol', {
        instrumentToken: symbolData.instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Batch upsert multiple symbols for high-performance bulk operations
   */
  async batchUpsertSymbols(symbols: SymbolUpsert[]): Promise<{
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    processingTimeMs: number;
    errors: string[];
  }> {
    try {
      this.logger.log(`Starting batch upsert for ${symbols.length} symbols`);

      const startTime = this.dateTimeUtils.getTime();
      let successCount = 0;
      let failureCount = 0;
      const errors: string[] = [];

      // Process symbols in batches of 1000
      const batchSize = 1000;
      for (let i = 0; i < symbols.length; i += batchSize) {
        const batch = symbols.slice(i, i + batchSize);

        try {
          // Validate batch
          const validatedBatch = batch.map((symbol) => SymbolUpsertSchema.parse(symbol));

          // Process each symbol in the batch
          for (const symbol of validatedBatch) {
            const now = this.dateTimeUtils.getUtcNow();
            const values = [
              symbol.instrumentToken,
              symbol.exchangeToken,
              symbol.tradingSymbol,
              symbol.name,
              symbol.lastPrice,
              symbol.tickSize,
              symbol.lotSize,
              symbol.expiry,
              symbol.strike,
              symbol.instrumentType,
              symbol.segment,
              symbol.exchange,
              symbol.isActive,
              now, // downloaded_at
              now, // updated_at
              now, // timestamp
            ];

            await this.executeQuery(SYMBOL_QUERIES.UPSERT_SYMBOL, values);
            successCount++;
          }
        } catch (error) {
          failureCount += batch.length;
          const errorMessage = this.errorUtils.getErrorMessage(error);
          errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMessage}`);

          this.logger.warn(`Batch upsert failed for batch starting at index ${i}`, {
            error: errorMessage,
            batchSize: batch.length,
          });
        }
      }

      const processingTimeMs = this.dateTimeUtils.getTime() - startTime;

      this.logger.log(`Batch upsert completed`, {
        totalProcessed: symbols.length,
        successCount,
        failureCount,
        processingTimeMs,
      });

      return {
        totalProcessed: symbols.length,
        successCount,
        failureCount,
        processingTimeMs,
        errors,
      };
    } catch (error) {
      this.logger.error('Batch upsert failed', {
        symbolCount: symbols.length,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Find symbol by instrument token
   */
  async findByInstrumentToken(instrumentToken: string): Promise<Symbol | null> {
    try {
      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.GET_SYMBOL_BY_TOKEN, [
        instrumentToken,
      ]);

      if (result.rowCount === 0) {
        return null;
      }

      return this.parseRawToSymbol(result.data[0]);
    } catch (error) {
      this.logger.error('Failed to find symbol by instrument token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Query symbols with filters
   */
  async querySymbols(filters: SymbolQueryFilters): Promise<Symbol[]> {
    try {
      // Validate filters
      const validatedFilters = SymbolQueryFiltersSchema.parse(filters);

      this.logger.debug('Querying symbols with filters', validatedFilters);

      let query = SYMBOL_QUERIES.GET_LATEST_SYMBOLS;
      const params: unknown[] = [];
      let paramIndex = 1;

      // Add filters
      const whereConditions: string[] = [];

      if (validatedFilters.exchange) {
        whereConditions.push(`exchange = $${paramIndex}`);
        params.push(validatedFilters.exchange);
        paramIndex++;
      }

      if (validatedFilters.segment) {
        whereConditions.push(`segment = $${paramIndex}`);
        params.push(validatedFilters.segment);
        paramIndex++;
      }

      if (validatedFilters.isActive !== undefined) {
        whereConditions.push(`is_active = $${paramIndex}`);
        params.push(validatedFilters.isActive);
        paramIndex++;
      }

      if (whereConditions.length > 0) {
        query += ` AND ${whereConditions.join(' AND ')}`;
      }

      query += ` ORDER BY trading_symbol`;

      // Add pagination
      if (validatedFilters.limit) {
        query += ` LIMIT $${paramIndex}`;
        params.push(validatedFilters.limit);
        paramIndex++;
      }

      if (validatedFilters.offset) {
        query += ` OFFSET $${paramIndex}`;
        params.push(validatedFilters.offset);
      }

      const result = await this.executeQuery<Record<string, unknown>>(query, params);
      return result.data.map((row) => this.parseRawToSymbol(row));
    } catch (error) {
      this.logger.error('Failed to query symbols', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Search symbols by trading symbol or name
   */
  async searchSymbols(searchTerm: string, limit = 50, offset = 0): Promise<Symbol[]> {
    try {
      const searchPattern = `%${searchTerm}%`;
      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.SEARCH_SYMBOLS, [
        searchPattern,
        limit,
        offset,
      ]);

      return result.data.map((row) => this.parseRawToSymbol(row));
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get symbols by exchange
   */
  async getSymbolsByExchange(exchange: string): Promise<Symbol[]> {
    try {
      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.GET_SYMBOLS_BY_EXCHANGE, [
        exchange,
      ]);

      return result.data.map((row) => this.parseRawToSymbol(row));
    } catch (error) {
      this.logger.error('Failed to get symbols by exchange', {
        exchange,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get active symbols count
   */
  async getActiveSymbolsCount(): Promise<number> {
    try {
      const result = await this.executeQuery<{ count: number }>(SYMBOL_QUERIES.GET_ACTIVE_SYMBOLS_COUNT);
      return Number(result.data[0].count);
    } catch (error) {
      this.logger.error('Failed to get active symbols count', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }
}
