import { Injectable, Logger } from '@nestjs/common';
import { Sender } from '@questdb/nodejs-client';
import { EnvService } from '@app/core/env';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { KiteInstrumentRaw, KiteInstrumentRawSchema } from './symbol.schema';
import { createWriteStream, createReadStream, promises as fs } from 'fs';
import { pipeline } from 'stream/promises';
import { Readable } from 'stream';
import { tmpdir } from 'os';
import { join } from 'path';
import * as csvParser from 'csv-parser';

/**
 * Kite Instruments API Integration Service
 *
 * Handles fetching instrument master data from Zerodha Kite Connect API
 * and bulk insertion into QuestDB using the official QuestDB Node.js client.
 *
 * Features:
 * - Fetches CSV data from Kite instruments API
 * - Parses and validates instrument data
 * - Bulk insert operations using QuestDB Node.js client
 * - Error handling and retry logic
 * - Progress tracking and logging
 */
@Injectable()
export class SymbolService {
  private readonly logger = new Logger(SymbolService.name);
  private readonly KITE_INSTRUMENTS_URL = 'https://api.kite.trade/instruments';
  private readonly BATCH_SIZE = 1000;

  constructor(
    private readonly envService: EnvService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  /**
   * Fetch and process all instruments from Kite API using streaming for memory efficiency
   */
  async fetchAndStoreInstruments(): Promise<{
    totalFetched: number;
    totalInserted: number;
    errors: string[];
    duration: number;
  }> {
    const startTime = this.dateTimeUtils.getTime();
    const errors: string[] = [];
    let totalFetched = 0;
    let totalInserted = 0;
    let tempFilePath: string | null = null;

    try {
      this.logger.log('Starting Kite instruments fetch and store operation with streaming');

      // Step 1: Download CSV to temporary file
      tempFilePath = await this.downloadInstrumentsToTempFile();
      this.logger.log(`Downloaded CSV to temporary file: ${tempFilePath}`);

      // Step 2: Stream process and insert into QuestDB
      const result = await this.streamProcessAndInsertInstruments(tempFilePath);
      totalFetched = result.totalProcessed;
      totalInserted = result.totalInserted;
      errors.push(...result.errors);

      this.logger.log(`Successfully processed ${totalFetched} instruments, inserted ${totalInserted}`);

      const duration = this.dateTimeUtils.getTime() - startTime;

      return {
        totalFetched,
        totalInserted,
        errors,
        duration,
      };
    } catch (error) {
      const errorMessage = this.errorUtils.getErrorMessage(error);
      errors.push(errorMessage);

      this.logger.error('Failed to fetch and store instruments', {
        error: errorMessage,
        totalFetched,
        totalInserted,
      });

      const duration = this.dateTimeUtils.getTime() - startTime;

      return {
        totalFetched,
        totalInserted,
        errors,
        duration,
      };
    } finally {
      // Cleanup temporary file
      if (tempFilePath) {
        try {
          await fs.unlink(tempFilePath);
          this.logger.debug(`Cleaned up temporary file: ${tempFilePath}`);
        } catch (error) {
          this.logger.warn('Failed to cleanup temporary file', {
            filePath: tempFilePath,
            error: this.errorUtils.getErrorMessage(error),
          });
        }
      }
    }
  }

  /**
   * Download instruments CSV data from Kite API to a temporary file using streaming
   */
  private async downloadInstrumentsToTempFile(): Promise<string> {
    try {
      this.logger.log('Downloading instruments CSV from Kite API to temporary file');

      // Create temporary file path
      const timestamp = this.dateTimeUtils.getUtcNow().replace(/[:.]/g, '-');
      const tempFilePath = join(tmpdir(), `instruments-${timestamp}.csv`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // Increased timeout for download

      const response = await fetch(this.KITE_INSTRUMENTS_URL, {
        method: 'GET',
        headers: {
          'User-Agent': 'PatternTrade-API/1.0.0',
          Accept: 'text/csv',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('No response body received from Kite instruments API');
      }

      // Stream the response to temporary file
      const writeStream = createWriteStream(tempFilePath);

      // Convert Web Streams ReadableStream to Node.js Readable stream

      const reader = response.body.getReader();
      const nodeReadableStream = new Readable({
        async read() {
          try {
            const { done, value } = await reader.read();
            if (done) {
              this.push(null);
            } else {
              this.push(Buffer.from(value));
            }
          } catch (error) {
            this.destroy(error as Error);
          }
        },
      });

      await pipeline(nodeReadableStream, writeStream);

      // Verify file was created and has content
      const stats = await fs.stat(tempFilePath);
      if (stats.size === 0) {
        throw new Error('Downloaded file is empty');
      }

      this.logger.log('Successfully downloaded instruments CSV to temporary file', {
        filePath: tempFilePath,
        fileSize: stats.size,
        contentType: response.headers.get('content-type'),
      });

      return tempFilePath;
    } catch (error) {
      this.logger.error('Failed to download instruments CSV to temporary file', {
        url: this.KITE_INSTRUMENTS_URL,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Stream process CSV file and insert instruments into QuestDB
   */
  private async streamProcessAndInsertInstruments(tempFilePath: string): Promise<{
    totalProcessed: number;
    totalInserted: number;
    errors: string[];
  }> {
    let sender: Sender | null = null;
    let totalProcessed = 0;
    let totalInserted = 0;
    const errors: string[] = [];
    const batch: KiteInstrumentRaw[] = [];

    try {
      this.logger.log('Starting stream processing of instruments CSV', {
        filePath: tempFilePath,
        batchSize: this.BATCH_SIZE,
      });

      // Create QuestDB sender
      const questdbHost = this.envService.get('QUESTDB_HOST') || 'localhost';
      const questdbPort = this.envService.get('QUESTDB_ILP_PORT') || 9009;
      sender = Sender.fromConfig(`http::addr=${questdbHost}:${questdbPort};`);

      const now = this.dateTimeUtils.getUtcNow();
      const timestamp = new Date(now).getTime() * 1000; // Microseconds

      // Create readable stream and CSV parser
      const readStream = createReadStream(tempFilePath);
      const csvStream = csvParser();

      // Process CSV stream
      await new Promise<void>((resolve, reject) => {
        let isFirstRow = true;

        readStream
          .pipe(csvStream)
          .on('data', (row: Record<string, string>) => {
            try {
              // Skip header row if it's detected as data
              if (isFirstRow) {
                isFirstRow = false;
                // Check if this looks like a header row
                if (row.instrument_token === 'instrument_token' || row.tradingsymbol === 'tradingsymbol') {
                  return;
                }
              }

              totalProcessed++;

              // Map and validate instrument data
              const instrument = this.mapRowToInstrument(row);
              const validatedInstrument = KiteInstrumentRawSchema.safeParse(instrument);

              if (!validatedInstrument.success) {
                errors.push(`Row ${totalProcessed}: ${validatedInstrument.error.message}`);
                return;
              }

              batch.push(validatedInstrument.data);

              // Process batch when it reaches the batch size
              if (batch.length >= this.BATCH_SIZE) {
                if (!sender) {
                  throw new Error('QuestDB sender not initialized');
                }
                // Handle async operation without making the event handler async
                this.insertBatch(sender, batch, now, timestamp)
                  .then((inserted) => {
                    totalInserted += inserted;
                    batch.length = 0; // Clear batch

                    this.logger.debug(`Processed batch`, {
                      totalProcessed,
                      totalInserted,
                      batchSize: this.BATCH_SIZE,
                    });
                  })
                  .catch((error) => {
                    errors.push(`Batch insert error: ${this.errorUtils.getErrorMessage(error)}`);
                  });
              }
            } catch (error) {
              errors.push(`Row ${totalProcessed}: ${this.errorUtils.getErrorMessage(error)}`);
            }
          })
          .on('end', () => {
            try {
              // Process remaining items in batch
              if (batch.length > 0) {
                if (!sender) {
                  reject(new Error('QuestDB sender not initialized'));
                  return;
                }
                this.insertBatch(sender, batch, now, timestamp)
                  .then((inserted) => {
                    totalInserted += inserted;
                    resolve();
                  })
                  .catch((error) => {
                    reject(error instanceof Error ? error : new Error(String(error)));
                  });
              } else {
                resolve();
              }
            } catch (error) {
              reject(error instanceof Error ? error : new Error(String(error)));
            }
          })
          .on('error', (error) => {
            reject(error);
          });
      });

      this.logger.log('Stream processing completed successfully', {
        totalProcessed,
        totalInserted,
        errors: errors.length,
      });

      return {
        totalProcessed,
        totalInserted,
        errors,
      };
    } catch (error) {
      this.logger.error('Failed to stream process instruments', {
        error: this.errorUtils.getErrorMessage(error),
        totalProcessed,
        totalInserted,
      });
      throw error;
    } finally {
      // Always close the sender
      if (sender) {
        try {
          await sender.close();
        } catch (error) {
          this.logger.warn('Failed to close QuestDB sender', {
            error: this.errorUtils.getErrorMessage(error),
          });
        }
      }
    }
  }

  /**
   * Map CSV row to instrument object (optimized for streaming)
   */
  private mapRowToInstrument(row: Record<string, string>): Partial<KiteInstrumentRaw> {
    const instrument: Record<string, unknown> = {};

    // Map CSV fields to our schema fields
    instrument.instrument_token = row.instrument_token?.trim() || '';
    instrument.exchange_token = row.exchange_token?.trim() || '';
    instrument.tradingsymbol = row.tradingsymbol?.trim() || '';
    instrument.name = row.name?.trim() || '';
    instrument.last_price = row.last_price ? parseFloat(row.last_price) : 0;
    instrument.expiry = row.expiry?.trim() || null;
    instrument.strike = row.strike ? parseFloat(row.strike) : null;
    instrument.tick_size = row.tick_size ? parseFloat(row.tick_size) : 0;
    instrument.lot_size = row.lot_size ? parseInt(row.lot_size) : 0;
    instrument.instrument_type = row.instrument_type?.trim() || '';
    instrument.segment = row.segment?.trim() || '';
    instrument.exchange = row.exchange?.trim() || '';

    return instrument;
  }

  /**
   * Insert a batch of instruments into QuestDB
   */
  private async insertBatch(
    sender: Sender,
    batch: KiteInstrumentRaw[],
    now: string,
    timestamp: number,
  ): Promise<number> {
    try {
      // Add each instrument to the sender
      for (const instrument of batch) {
        await sender
          .table('symbol_master')
          .stringColumn('instrument_token', instrument.instrument_token)
          .stringColumn('exchange_token', instrument.exchange_token)
          .stringColumn('trading_symbol', instrument.tradingsymbol)
          .stringColumn('name', instrument.name || '')
          .floatColumn('last_price', instrument.last_price || 0)
          .floatColumn('tick_size', instrument.tick_size || 0)
          .intColumn('lot_size', instrument.lot_size || 0)
          .stringColumn('expiry', instrument.expiry || '')
          .floatColumn('strike', instrument.strike || 0)
          .stringColumn('instrument_type', instrument.instrument_type)
          .stringColumn('segment', instrument.segment)
          .stringColumn('exchange', instrument.exchange)
          .booleanColumn('is_active', true)
          .stringColumn('downloaded_at', now)
          .stringColumn('updated_at', now)
          .timestampColumn('timestamp', timestamp)
          .at(timestamp);
      }

      // Flush the batch
      await sender.flush();
      return batch.length;
    } catch (error) {
      this.logger.error('Failed to insert batch', {
        batchSize: batch.length,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean;
    lastFetch?: Date;
    apiStatus: 'available' | 'unavailable' | 'unknown';
    error?: string;
  }> {
    try {
      // Test API availability with a HEAD request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(this.KITE_INSTRUMENTS_URL, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      return {
        isHealthy: response.ok,
        apiStatus: response.ok ? 'available' : 'unavailable',
        lastFetch: new Date(),
      };
    } catch (error) {
      return {
        isHealthy: false,
        apiStatus: 'unavailable',
        error: this.errorUtils.getErrorMessage(error),
      };
    }
  }
}
