# Symbol Module

The Symbol Module provides comprehensive symbol  data management for the PatternTrade API. It handles downloading, storing, and retrieving symbol data from Zerodha Kite Connect API with robust background processing, audit logging, and microservice communication.

## Features

- ✅ **Symbol  Data Management**: Complete CRUD operations for symbol data
- ✅ **Zerodha Kite Connect Integration**: Real-time symbol data from Kite API
- ✅ **QuestDB Time-Series Storage**: Optimized storage for high-frequency data
- ✅ **Background Job Processing**: BullMQ-based download workers with retry logic
- ✅ **Cron Job Scheduling**: Daily automated symbol data updates
- ✅ **Audit Logging**: Comprehensive audit trail with PostgreSQL
- ✅ **Microservice Communication**: gRPC-style Redis transport
- ✅ **Health Monitoring**: Real-time health checks and statistics
- ✅ **Error Handling**: Robust error handling with retry mechanisms
- ✅ **Type Safety**: Full TypeScript support with Zod validation

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │   Datastore     │    │  Symbol Module  │
│                 │    │  Application    │    │                 │
│ SymbolController│◄──►│SymbolController │◄──►│  SymbolService  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐            │
                       │   QuestDB       │◄───────────┤
                       │ (Time-Series)   │            │
                       └─────────────────┘            │
                                                       │
                       ┌─────────────────┐            │
                       │   PostgreSQL    │◄───────────┤
                       │ (Audit Logs)    │            │
                       └─────────────────┘            │
                                                       │
                       ┌─────────────────┐            │
                       │     Redis       │◄───────────┤
                       │ (Queue/Cache)   │            │
                       └─────────────────┘            │
                                                       │
                       ┌─────────────────┐            │
                       │  Kite Connect   │◄───────────┘
                       │      API        │
                       └─────────────────┘
```

## Quick Start

### Installation

```bash
# Install dependencies
pnpm install

# Setup environment variables
cp .env.example .env
# Configure ZERODHA_API_KEY and database connections
```

### Basic Usage

```typescript
import { SymbolService, SymbolRepository } from '@app/symbol';

// Inject services in your module
@Injectable()
export class YourService {
  constructor(
    private readonly symbolService: SymbolService,
    private readonly symbolRepository: SymbolRepository,
  ) {}

  // Download symbols from Kite API
  async downloadSymbols() {
    const result = await this.symbolService.downloadAndStoreSymbol('NSE', 'NSE');
    console.log(`Downloaded ${result.symbolsProcessed} symbols`);
  }

  // Find symbol by instrument token
  async getSymbol(instrumentToken: string) {
    return await this.symbolRepository.findByInstrumentToken(instrumentToken);
  }

  // Search symbols
  async searchSymbols(query: string) {
    return await this.symbolRepository.searchSymbols(query, 50);
  }
}
```

### Queue Operations

```typescript
import { SymbolDownloadQueueService } from '@app/symbol';

@Injectable()
export class QueueExample {
  constructor(
    private readonly queueService: SymbolDownloadQueueService,
  ) {}

  // Trigger download job
  async triggerDownload() {
    const job = await this.queueService.addSymbolDownloadJob('NSE', 'NSE', {
      forceRefresh: true,
      batchSize: 500,
      priority: 1,
    });
    
    console.log(`Job ${job.jobId} scheduled`);
    return job;
  }

  // Check job status
  async checkStatus(jobId: string) {
    const status = await this.queueService.getJobStatus(jobId);
    console.log(`Job ${jobId} is ${status?.state}`);
    return status;
  }

  // Get queue statistics
  async getStats() {
    return await this.queueService.getQueueStats();
  }
}
```

## API Reference

### SymbolService

Main service for symbol operations.

#### Methods

- `downloadAndStoreSymbol(exchange?, segment?, forceRefresh?, requestId?)`: Download and store symbols
- `findSymbolByToken(instrumentToken)`: Find symbol by instrument token
- `searchSymbols(query, limit?)`: Search symbols by trading symbol
- `getHealthStatus()`: Get service health status

### SymbolRepository

Repository for symbol data operations.

#### Methods

- `upsertSymbol(symbolData)`: Insert or update symbol
- `batchUpsertSymbols(symbols, batchSize)`: Batch upsert operations
- `findByInstrumentToken(token)`: Find by instrument token
- `findSymbols(filters)`: Find symbols with filters
- `searchSymbols(query, limit, offset)`: Search symbols
- `getStats()`: Get repository statistics
- `getHealthStatus()`: Get repository health

### SymbolDownloadQueueService

Queue service for background downloads.

#### Methods

- `addSymbolDownloadJob(exchange, segment, options?)`: Add download job
- `addBulkSymbolDownloadJobs(exchanges, options?)`: Add multiple jobs
- `getJobStatus(jobId)`: Get job status
- `getJobStatusByRequestId(requestId)`: Get jobs by request ID
- `cancelJob(jobId)`: Cancel job
- `getQueueStats()`: Get queue statistics
- `cleanOldJobs(options?)`: Clean old jobs

### SymbolAuditService

Audit logging service.

#### Methods

- `logDownloadStart(exchange, segment, requestId, jobId?, metadata?)`: Log download start
- `logDownloadComplete(auditId, result, requestId)`: Log download completion
- `logDownloadFailure(auditId, error, requestId, exchange, segment, duration)`: Log failure
- `getAuditLogsByRequestId(requestId)`: Get audit logs by request ID
- `getAuditStats()`: Get audit statistics

## Configuration

### Environment Variables

```bash
# Zerodha Kite Connect
ZERODHA_API_KEY=your_kite_api_key

# QuestDB Configuration
QUESTDB_HOST=localhost
QUESTDB_PORT=9000
QUESTDB_USERNAME=admin
QUESTDB_PASSWORD=quest

# PostgreSQL Configuration (for audit logs)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=patterntrade
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=password

# Redis Configuration (for queues)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### Module Configuration

```typescript
import { Module } from '@nestjs/common';
import { SymbolModule } from '@app/symbol';
import { CoreModule } from '@app/core';

@Module({
  imports: [
    CoreModule, // Required for database and queue services
    SymbolModule,
  ],
})
export class AppModule {}
```

## Schemas and Types

### Symbol

```typescript
interface Symbol {
  instrumentToken: string;
  exchangeToken: string;
  tradingSymbol: string;
  name: string;
  lastPrice: number;
  expiry?: Date;
  strike?: number;
  tickSize: number;
  lotSize: number;
  instrumentType: 'EQ' | 'FUT' | 'CE' | 'PE' | 'INDEX';
  segment: string;
  exchange: string;
  isActive: boolean;
  downloadedAt: Date;
  updatedAt: Date;
}
```

### SymbolDownloadRequest

```typescript
interface SymbolDownloadRequest {
  exchange: string;
  segment: string;
  forceRefresh?: boolean;
  batchSize?: number;
  requestId?: string;
  priority?: number;
}
```

### SymbolDownloadResult

```typescript
interface SymbolDownloadResult {
  exchange: string;
  segment: string;
  symbolsProcessed: number;
  symbolsAdded: number;
  symbolsUpdated: number;
  symbolsSkipped: number;
  duration: number;
  startedAt: Date;
  completedAt: Date;
  errors: string[];
}
```

## Cron Jobs

The module automatically sets up a daily cron job to download symbol data:

- **Schedule**: Daily at 8:00 AM IST (2:30 AM UTC)
- **Operation**: Downloads all symbols from all exchanges
- **Configuration**: Automatic retry on failure, comprehensive logging

### Manual Cron Management

```typescript
// Trigger immediate download
await queueService.addSymbolDownloadJob('ALL', 'ALL', {
  forceRefresh: true,
  priority: 1,
});

// Check cron job status
const stats = await queueService.getQueueStats();
console.log('Cron job status:', stats.cronJobStatus);
console.log('Next run:', stats.nextCronRun);
```

## Health Monitoring

### Health Check Endpoints

The module provides comprehensive health monitoring:

```typescript
// Service health
const health = await symbolService.getHealthStatus();
console.log('Service healthy:', health.isHealthy);

// Repository health
const repoHealth = await symbolRepository.getHealthStatus();
console.log('Repository healthy:', repoHealth.isHealthy);

// Queue health
const queueHealth = await queueService.getComprehensiveHealthStatus();
console.log('Queue healthy:', queueHealth.isHealthy);

// Audit health
const auditHealth = await auditService.getHealthStatus();
console.log('Audit healthy:', auditHealth.isHealthy);
```

### Statistics

```typescript
// Repository statistics
const repoStats = await symbolRepository.getStats();
console.log('Total symbols:', repoStats.totalRecords);
console.log('Active symbols:', repoStats.activeRecords);
console.log('Last update:', repoStats.lastUpdate);

// Queue statistics
const queueStats = await queueService.getQueueStats();
console.log('Jobs waiting:', queueStats.waiting);
console.log('Jobs active:', queueStats.active);
console.log('Jobs completed:', queueStats.completed);
console.log('Jobs failed:', queueStats.failed);

// Audit statistics
const auditStats = await auditService.getAuditStats();
console.log('Audit operations:', auditStats);
```

## Error Handling

The module implements comprehensive error handling:

### Retry Mechanisms

- **API Calls**: 3 retries with exponential backoff
- **Database Operations**: Automatic retry on connection failures
- **Queue Jobs**: Configurable retry attempts (default: 3)

### Error Types

```typescript
// Service errors
try {
  await symbolService.downloadAndStoreSymbol();
} catch (error) {
  if (error.message.includes('API rate limit')) {
    // Handle rate limiting
  } else if (error.message.includes('Database connection')) {
    // Handle database issues
  }
}

// Repository errors
try {
  await symbolRepository.upsertSymbol(symbolData);
} catch (error) {
  // Handle validation or storage errors
}
```

## Testing

### Unit Tests

```bash
# Run all symbol module tests
pnpm test libs/symbol

# Run specific test files
pnpm test symbol.service.spec.ts
pnpm test symbol-.repository.spec.ts
pnpm test symbol-download.queue.spec.ts
```

### Integration Tests

```bash
# Run integration tests (requires database connections)
pnpm test:integration libs/symbol

# Run with test containers
pnpm test:integration:docker libs/symbol
```

### Test Coverage

```bash
# Generate coverage report
pnpm test:cov libs/symbol
```

## Performance Considerations

### Optimization Tips

1. **Batch Operations**: Use `batchUpsertSymbols` for large datasets
2. **Pagination**: Use appropriate limit/offset for large queries
3. **Caching**: Repository implements intelligent caching
4. **Connection Pooling**: QuestDB and PostgreSQL use connection pools
5. **Queue Concurrency**: Worker processes one job at a time to prevent conflicts

### Monitoring

- Monitor queue depth and processing times
- Track database connection pool usage
- Monitor API rate limits and response times
- Set up alerts for failed jobs and health check failures

## Troubleshooting

### Common Issues

1. **API Rate Limits**: Kite Connect has rate limits - implement backoff
2. **Database Connections**: Ensure proper connection pool configuration
3. **Queue Stalls**: Monitor Redis memory usage and connection health
4. **Cron Job Failures**: Check logs and ensure proper timezone configuration

### Debug Mode

```bash
# Enable debug logging
DEBUG=symbol:* pnpm start

# Check service health
curl http://localhost:3006/health/symbol

# Monitor queue
curl http://localhost:3006/symbols/stats/queue
```

## Contributing

1. Follow the established patterns for services and repositories
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Ensure proper error handling and logging
5. Follow TypeScript strict mode requirements

## REST API Endpoints

### Symbol Data Endpoints

#### GET /symbols
Get symbols with filters and pagination.

**Query Parameters:**
- `exchange` (optional): Filter by exchange (NSE, BSE, NFO, BFO, CDS, MCX)
- `segment` (optional): Filter by segment
- `instrumentType` (optional): Filter by instrument type (EQ, FUT, CE, PE, INDEX)
- `tradingSymbol` (optional): Filter by trading symbol pattern
- `isActive` (optional): Filter by active status
- `limit` (optional): Maximum results (default: 100, max: 1000)
- `offset` (optional): Number of results to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "instrumentToken": "738561",
      "exchangeToken": "2885",
      "tradingSymbol": "RELIANCE",
      "name": "Reliance Industries Limited",
      "lastPrice": 2450.50,
      "tickSize": 0.05,
      "lotSize": 1,
      "instrumentType": "EQ",
      "segment": "NSE",
      "exchange": "NSE",
      "isActive": true,
      "downloadedAt": "2024-01-15T08:00:00Z",
      "updatedAt": "2024-01-15T08:00:00Z"
    }
  ],
  "meta": {
    "total": 1000,
    "limit": 100,
    "offset": 0,
    "hasMore": true
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### GET /symbols/:instrumentToken
Get symbol by instrument token.

**Parameters:**
- `instrumentToken`: Instrument token of the symbol

**Response:**
```json
{
  "success": true,
  "data": {
    "instrumentToken": "738561",
    "tradingSymbol": "RELIANCE",
    // ... other symbol fields
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### GET /symbols/search/:searchTerm
Search symbols by trading symbol pattern.

**Parameters:**
- `searchTerm`: Search term for trading symbol

**Query Parameters:**
- `limit` (optional): Maximum results (default: 50, max: 100)
- `offset` (optional): Number of results to skip (default: 0)

**Response:**
```json
{
  "success": true,
  "data": [
    // Array of matching symbols
  ],
  "meta": {
    "searchTerm": "RELIANCE",
    "limit": 50,
    "offset": 0,
    "count": 5
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Download Operations

#### POST /symbols/download
Trigger symbol download.

**Request Body:**
```json
{
  "exchange": "NSE",
  "segment": "NSE",
  "forceRefresh": false,
  "batchSize": 500,
  "requestId": "manual-download-123",
  "priority": 5
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_123456",
    "requestId": "manual-download-123",
    "scheduledAt": "2024-01-15T10:30:00Z",
    "estimatedCompletion": "2024-01-15T10:35:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### GET /symbols/download/status/:jobId
Get download job status.

**Parameters:**
- `jobId`: Job ID of the download operation

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "job_123456",
    "state": "completed",
    "progress": 100,
    "attempts": 1,
    "maxAttempts": 3,
    "createdAt": "2024-01-15T10:30:00Z",
    "processedAt": "2024-01-15T10:31:00Z",
    "finishedAt": "2024-01-15T10:35:00Z",
    "result": {
      "symbolsProcessed": 1000,
      "symbolsAdded": 500,
      "symbolsUpdated": 500,
      "errors": []
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### POST /symbols/download/immediate
Trigger immediate download for all exchanges.

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_789012",
    "requestId": "immediate_345678",
    "scheduledAt": "2024-01-15T10:30:00Z",
    "priority": "HIGH"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Statistics and Health

#### GET /symbols/stats
Get symbol service statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "repository": {
      "totalRecords": 10000,
      "activeRecords": 9500,
      "lastUpdate": "2024-01-15T08:00:00Z",
      "exchangeBreakdown": {
        "NSE": 5000,
        "BSE": 3000,
        "NFO": 2000
      }
    },
    "queue": {
      "waiting": 5,
      "active": 1,
      "completed": 100,
      "failed": 2,
      "cronJobStatus": "active",
      "nextCronRun": "2024-01-16T08:00:00Z"
    },
    "summary": {
      "totalSymbols": 10000,
      "activeSymbols": 9500,
      "lastUpdate": "2024-01-15T08:00:00Z",
      "queueHealth": "healthy",
      "overallHealth": "healthy"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### GET /symbols/health
Get symbol service health.

**Response:**
```json
{
  "success": true,
  "data": {
    "isHealthy": true,
    "services": {
      "repository": {
        "isHealthy": true,
        "tableExists": true,
        "connectionStatus": true,
        "recordCount": 10000
      },
      "queue": {
        "isHealthy": true,
        "cronJobStatus": "active",
        "issues": []
      },
      "audit": {
        "isHealthy": true,
        "recordCount": 500,
        "issues": []
      }
    },
    "issues": [],
    "lastCheck": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message description",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**HTTP Status Codes:**
- `200`: Success
- `201`: Created (for POST operations)
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `404`: Not Found
- `429`: Too Many Requests (rate limiting)
- `500`: Internal Server Error

## License

This module is part of the PatternTrade API project.
