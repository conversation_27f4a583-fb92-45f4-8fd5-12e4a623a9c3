/**
 * Base QuestDB repository implementation for PatternTrade API
 *
 * Provides foundational QuestDB repository functionality with:
 * - Common CRUD operations optimized for time-series data
 * - Batch processing capabilities for high-volume operations
 * - Comprehensive error handling with QuestDB-specific errors
 * - Time-series specific operations and utilities
 * - TypeScript generics for type-safe operations
 * - Consistent logging and performance monitoring
 */

import { Injectable, Logger } from '@nestjs/common';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import type { QuestDBService } from '@app/core/questdb';
import {
  IBaseQuestDBRepository,
  ITimeSeriesRepository,
  IUpsertRepository,
  QuestDBQueryResult,
  TimeSeriesInsertOptions,
  BatchInsertOptions,
  BatchOperationResult,
  TableCreationOptions,
  TimeSeriesQueryOptions,
} from './base-questdb-repository.interface';
import {
  QuestDBRepositoryError,
  QuestDBEntityNotFoundError,
  QuestDBQueryExecutionError,
  QuestDBBatchOperationError,
  QuestDBTimeSeriesError,
  QuestDBRepositoryContext,
  QuestDBTableSchema,
  createQuestDBRepositoryError,
} from './questdb-repository.types';

/**
 * Abstract base repository class for QuestDB operations
 *
 * This abstract class provides:
 * - Standard CRUD operations optimized for QuestDB
 * - Time-series specific operations and utilities
 * - Batch processing with configurable batch sizes
 * - Comprehensive error handling and logging
 * - Performance monitoring and statistics
 * - Type-safe operations with TypeScript generics
 */
@Injectable()
export abstract class BaseQuestDBRepository<T, CreateData, UpdateData, ID = number>
  implements
    IBaseQuestDBRepository<T, CreateData, UpdateData, ID>,
    ITimeSeriesRepository<T, CreateData>,
    IUpsertRepository<T, CreateData>
{
  protected readonly logger: Logger;
  protected readonly context: QuestDBRepositoryContext;

  constructor(
    protected readonly questdbService: QuestDBService,
    protected readonly dateTimeUtils: DateTimeUtilsService,
    protected readonly errorUtils: ErrorUtilsService,
    repositoryName: string,
    tableName: string,
  ) {
    this.logger = new Logger(repositoryName);
    this.context = {
      repositoryName,
      tableName,
      operation: 'unknown',
    };
  }

  // ==================== ABSTRACT METHODS ====================

  /**
   * Get the table schema definition
   * Must be implemented by concrete repository classes
   */
  protected abstract getTableSchema(): QuestDBTableSchema;

  /**
   * Transform raw database result to entity type
   * Must be implemented by concrete repository classes
   */
  protected abstract transformToEntity(raw: Record<string, unknown>): T;

  /**
   * Transform entity creation data to database format
   * Must be implemented by concrete repository classes
   */
  protected abstract transformCreateData(data: CreateData): Record<string, unknown>;

  /**
   * Transform entity update data to database format
   * Must be implemented by concrete repository classes
   */
  protected abstract transformUpdateData(data: UpdateData): Record<string, unknown>;

  // ==================== CORE OPERATIONS ====================

  /**
   * Execute a raw SQL query with error handling and logging
   */
  async executeQuery<R = T>(query: string, params?: unknown[]): Promise<QuestDBQueryResult<R>> {
    return this.executeWithErrorHandling('executeQuery', async () => {
      this.logOperation('executeQuery', { query: query.substring(0, 100), paramCount: params?.length || 0 });

      const startTime = this.dateTimeUtils.getTime();
      const result = await this.questdbService.executeQuery<R>(query, params);
      const executionTime = this.dateTimeUtils.getTime() - startTime;

      this.logger.debug(`Query executed successfully`, {
        table: this.context.tableName,
        executionTime,
        rowCount: result.rowCount,
        query: query.substring(0, 200),
      });

      return result;
    });
  }

  /**
   * Insert a single record
   */
  async insert(data: CreateData): Promise<T> {
    return this.executeWithErrorHandling('insert', async () => {
      this.logOperation('insert', { data });

      const transformedData = this.transformCreateData(data);
      const schema = this.getTableSchema();

      // Build insert query dynamically
      const columns = Object.keys(transformedData);
      const placeholders = columns.map((_, index) => `$${index + 1}`);
      const values = Object.values(transformedData);

      const query = `
        INSERT INTO ${schema.tableName} (${columns.join(', ')})
        VALUES (${placeholders.join(', ')})
      `;

      const result = await this.executeQuery<T>(query, values);

      if (result.rowCount === 0) {
        throw createQuestDBRepositoryError.invalidData('Insert operation returned no rows');
      }

      this.logger.log(`Successfully inserted record into ${schema.tableName}`);
      return result.data[0];
    });
  }

  /**
   * Insert multiple records in batch
   */
  async batchInsert(data: CreateData[], options?: Partial<BatchInsertOptions>): Promise<BatchOperationResult> {
    return this.executeWithErrorHandling('batchInsert', async () => {
      const batchSize = options?.batchSize || 1000;
      const schema = this.getTableSchema();

      this.logOperation('batchInsert', {
        recordCount: data.length,
        batchSize,
        table: schema.tableName,
      });

      const startTime = this.dateTimeUtils.getTime();
      let totalProcessed = 0;
      let successCount = 0;
      let failureCount = 0;
      const errors: string[] = [];

      // Process data in batches
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);

        try {
          const transformedBatch = batch.map((item) => this.transformCreateData(item));

          if (transformedBatch.length > 0) {
            // Build batch insert query
            const columns = Object.keys(transformedBatch[0]);
            const valueRows = transformedBatch.map((row, rowIndex) => {
              const rowPlaceholders = columns.map((_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`);
              return `(${rowPlaceholders.join(', ')})`;
            });

            const allValues = transformedBatch.flatMap((row) => Object.values(row));

            const query = `
              INSERT INTO ${schema.tableName} (${columns.join(', ')})
              VALUES ${valueRows.join(', ')}
              ${options?.ignoreDuplicates ? 'ON CONFLICT DO NOTHING' : ''}
            `;

            const result = await this.executeQuery(query, allValues);
            successCount += result.rowCount;
          }

          totalProcessed += batch.length;
        } catch (error) {
          failureCount += batch.length;
          const errorMessage = this.errorUtils.getErrorMessage(error);
          errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMessage}`);

          this.logger.warn(`Batch insert failed for batch starting at index ${i}`, {
            error: errorMessage,
            batchSize: batch.length,
          });
        }
      }

      const processingTimeMs = this.dateTimeUtils.getTime() - startTime;

      this.logger.log(`Batch insert completed`, {
        table: schema.tableName,
        totalProcessed,
        successCount,
        failureCount,
        processingTimeMs,
      });

      return {
        totalProcessed,
        successCount,
        failureCount,
        processingTimeMs,
        errors,
      };
    });
  }

  /**
   * Find records by filter criteria
   */
  async findByFilters(filters: Record<string, unknown>, options?: TimeSeriesQueryOptions): Promise<T[]> {
    return this.executeWithErrorHandling('findByFilters', async () => {
      this.logOperation('findByFilters', { filters, options });

      const schema = this.getTableSchema();
      const whereConditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Build WHERE clause from filters
      for (const [key, value] of Object.entries(filters)) {
        if (value !== undefined && value !== null) {
          whereConditions.push(`${key} = $${paramIndex}`);
          params.push(value);
          paramIndex++;
        }
      }

      // Add time range filters if provided
      if (options?.startTime) {
        whereConditions.push(`timestamp >= $${paramIndex}`);
        params.push(options.startTime);
        paramIndex++;
      }

      if (options?.endTime) {
        whereConditions.push(`timestamp <= $${paramIndex}`);
        params.push(options.endTime);
        paramIndex++;
      }

      // Build ORDER BY clause
      let orderByClause = '';
      if (options?.orderBy && options.orderBy.length > 0) {
        const orderByParts = options.orderBy.map((order) => `${order.field} ${order.direction}`);
        orderByClause = `ORDER BY ${orderByParts.join(', ')}`;
      }

      // Build LIMIT and OFFSET clauses
      let limitClause = '';
      if (options?.limit) {
        limitClause = `LIMIT $${paramIndex}`;
        params.push(options.limit);
        paramIndex++;
      }

      let offsetClause = '';
      if (options?.offset) {
        offsetClause = `OFFSET $${paramIndex}`;
        params.push(options.offset);
        paramIndex++;
      }

      const query = `
        SELECT * FROM ${schema.tableName}
        ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''}
        ${orderByClause}
        ${limitClause}
        ${offsetClause}
      `.trim();

      const result = await this.executeQuery<Record<string, unknown>>(query, params);
      return result.data.map((row) => this.transformToEntity(row));
    });
  }

  /**
   * Find a single record by ID
   */
  async findById(id: ID): Promise<T | null> {
    return this.executeWithErrorHandling('findById', async () => {
      this.logOperation('findById', { id });

      const schema = this.getTableSchema();
      const query = `SELECT * FROM ${schema.tableName} WHERE id = $1 LIMIT 1`;

      const result = await this.executeQuery<Record<string, unknown>>(query, [id]);

      if (result.rowCount === 0) {
        return null;
      }

      return this.transformToEntity(result.data[0]);
    });
  }

  // ==================== ERROR HANDLING ====================

  /**
   * Execute operation with comprehensive error handling
   */
  protected async executeWithErrorHandling<R>(operation: string, fn: () => Promise<R>): Promise<R> {
    const originalOperation = this.context.operation;
    this.context.operation = operation;

    try {
      return await fn();
    } catch (error) {
      this.handleRepositoryError(operation, error);
      throw error; // Re-throw after handling
    } finally {
      this.context.operation = originalOperation;
    }
  }

  /**
   * Handle and log repository errors
   */
  protected handleRepositoryError(operation: string, error: unknown): void {
    const errorMessage = this.errorUtils.getErrorMessage(error);
    const errorStack = this.errorUtils.getErrorStack(error);

    this.logger.error(`${operation} operation failed`, {
      table: this.context.tableName,
      operation,
      error: errorMessage,
      stack: errorStack,
      context: this.context,
    });

    // Transform generic errors to QuestDB repository errors if needed
    if (!(error instanceof QuestDBRepositoryError)) {
      // This will be caught and re-thrown by the caller
      throw new QuestDBRepositoryError('QUERY_EXECUTION_FAILED', {
        message: `${operation} operation failed: ${errorMessage}`,
        cause: error,
        context: { operation, table: this.context.tableName },
      });
    }
  }

  /**
   * Log repository operations
   */
  protected logOperation(operation: string, details: Record<string, unknown>): void {
    this.logger.debug(`${operation} operation started`, {
      table: this.context.tableName,
      operation,
      ...details,
    });
  }

  // ==================== UPDATE AND DELETE OPERATIONS ====================

  /**
   * Update records by filter criteria
   */
  async updateByFilters(filters: Record<string, unknown>, updates: UpdateData): Promise<number> {
    return this.executeWithErrorHandling('updateByFilters', async () => {
      this.logOperation('updateByFilters', { filters, updates });

      const schema = this.getTableSchema();
      const transformedUpdates = this.transformUpdateData(updates);

      const whereConditions: string[] = [];
      const setClause: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Build SET clause
      for (const [key, value] of Object.entries(transformedUpdates)) {
        setClause.push(`${key} = $${paramIndex}`);
        params.push(value);
        paramIndex++;
      }

      // Build WHERE clause
      for (const [key, value] of Object.entries(filters)) {
        if (value !== undefined && value !== null) {
          whereConditions.push(`${key} = $${paramIndex}`);
          params.push(value);
          paramIndex++;
        }
      }

      if (whereConditions.length === 0) {
        throw createQuestDBRepositoryError.invalidData('Update operation requires at least one filter condition');
      }

      const query = `
        UPDATE ${schema.tableName}
        SET ${setClause.join(', ')}
        WHERE ${whereConditions.join(' AND ')}
      `;

      const result = await this.executeQuery(query, params);

      this.logger.log(`Updated ${result.rowCount} records in ${schema.tableName}`);
      return result.rowCount;
    });
  }

  /**
   * Delete records by filter criteria
   */
  async deleteByFilters(filters: Record<string, unknown>): Promise<number> {
    return this.executeWithErrorHandling('deleteByFilters', async () => {
      this.logOperation('deleteByFilters', { filters });

      const schema = this.getTableSchema();
      const whereConditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Build WHERE clause
      for (const [key, value] of Object.entries(filters)) {
        if (value !== undefined && value !== null) {
          whereConditions.push(`${key} = $${paramIndex}`);
          params.push(value);
          paramIndex++;
        }
      }

      if (whereConditions.length === 0) {
        throw createQuestDBRepositoryError.invalidData('Delete operation requires at least one filter condition');
      }

      const query = `
        DELETE FROM ${schema.tableName}
        WHERE ${whereConditions.join(' AND ')}
      `;

      const result = await this.executeQuery(query, params);

      this.logger.log(`Deleted ${result.rowCount} records from ${schema.tableName}`);
      return result.rowCount;
    });
  }

  /**
   * Count records matching filter criteria
   */
  async count(filters?: Record<string, unknown>): Promise<number> {
    return this.executeWithErrorHandling('count', async () => {
      this.logOperation('count', { filters });

      const schema = this.getTableSchema();
      const whereConditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Build WHERE clause if filters provided
      if (filters) {
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null) {
            whereConditions.push(`${key} = $${paramIndex}`);
            params.push(value);
            paramIndex++;
          }
        }
      }

      const query = `
        SELECT COUNT(*) as count
        FROM ${schema.tableName}
        ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''}
      `;

      const result = await this.executeQuery<{ count: number }>(query, params);
      return Number(result.data[0].count);
    });
  }

  // ==================== TABLE MANAGEMENT OPERATIONS ====================

  /**
   * Check if table exists
   */
  async tableExists(): Promise<boolean> {
    return this.executeWithErrorHandling('tableExists', async () => {
      const schema = this.getTableSchema();

      const query = `
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_name = $1
      `;

      const result = await this.executeQuery<{ count: number }>(query, [schema.tableName]);
      return Number(result.data[0].count) > 0;
    });
  }

  /**
   * Create table with indexes
   */
  async createTable(options: TableCreationOptions): Promise<void> {
    return this.executeWithErrorHandling('createTable', async () => {
      this.logOperation('createTable', { tableName: options.tableName });

      // Drop table if requested
      if (options.dropIfExists) {
        await this.dropTable(options.tableName);
      }

      // Create the main table
      await this.executeQuery(options.createTableSQL);
      this.logger.log(`Created table: ${options.tableName}`);

      // Create indexes if provided
      if (options.createIndexesSQL && options.createIndexesSQL.length > 0) {
        for (const indexSQL of options.createIndexesSQL) {
          try {
            await this.executeQuery(indexSQL);
          } catch (error) {
            // Indexes might already exist, log warning but continue
            this.logger.warn(`Index creation warning for table ${options.tableName}`, {
              error: this.errorUtils.getErrorMessage(error),
              indexSQL: indexSQL.substring(0, 100),
            });
          }
        }
        this.logger.log(`Created indexes for table: ${options.tableName}`);
      }
    });
  }

  /**
   * Drop table
   */
  async dropTable(tableName?: string): Promise<void> {
    return this.executeWithErrorHandling('dropTable', async () => {
      const targetTable = tableName || this.getTableSchema().tableName;
      this.logOperation('dropTable', { tableName: targetTable });

      const query = `DROP TABLE IF EXISTS ${targetTable}`;
      await this.executeQuery(query);

      this.logger.log(`Dropped table: ${targetTable}`);
    });
  }

  // ==================== TIME-SERIES OPERATIONS ====================

  /**
   * Insert time-series data with designated timestamp
   */
  async insertTimeSeries(options: TimeSeriesInsertOptions): Promise<T> {
    return this.executeWithErrorHandling('insertTimeSeries', async () => {
      this.logOperation('insertTimeSeries', { table: options.table, timestamp: options.timestamp });

      const timestampValue =
        typeof options.timestamp === 'string'
          ? options.timestamp
          : this.dateTimeUtils.formatUtcDateTime(options.timestamp);

      const dataWithTimestamp = {
        ...options.data,
        [options.designatedTimestamp || 'timestamp']: timestampValue,
      };

      const columns = Object.keys(dataWithTimestamp);
      const placeholders = columns.map((_, index) => `$${index + 1}`);
      const values = Object.values(dataWithTimestamp);

      const query = `
        INSERT INTO ${options.table} (${columns.join(', ')})
        VALUES (${placeholders.join(', ')})
      `;

      const result = await this.executeQuery<T>(query, values);

      if (result.rowCount === 0) {
        throw new QuestDBTimeSeriesError('insert', options.timestamp);
      }

      this.logger.log(`Successfully inserted time-series data into ${options.table}`);
      return result.data[0];
    });
  }

  /**
   * Find latest records for each unique identifier
   */
  async findLatestByIdentifier(
    identifierField: string,
    filters?: Record<string, unknown>,
    limit?: number,
  ): Promise<T[]> {
    return this.executeWithErrorHandling('findLatestByIdentifier', async () => {
      this.logOperation('findLatestByIdentifier', { identifierField, filters, limit });

      const schema = this.getTableSchema();
      const timestampColumn = schema.designatedTimestamp || 'timestamp';

      const whereConditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Build WHERE clause from filters
      if (filters) {
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null) {
            whereConditions.push(`${key} = $${paramIndex}`);
            params.push(value);
            paramIndex++;
          }
        }
      }

      // Build LIMIT clause
      let limitClause = '';
      if (limit) {
        limitClause = `LIMIT $${paramIndex}`;
        params.push(limit);
      }

      const query = `
        SELECT * FROM ${schema.tableName}
        WHERE ${timestampColumn} = (
          SELECT MAX(${timestampColumn})
          FROM ${schema.tableName} s2
          WHERE s2.${identifierField} = ${schema.tableName}.${identifierField}
          ${whereConditions.length > 0 ? `AND ${whereConditions.join(' AND ')}` : ''}
        )
        ${whereConditions.length > 0 ? `AND ${whereConditions.join(' AND ')}` : ''}
        ORDER BY ${identifierField}
        ${limitClause}
      `;

      const result = await this.executeQuery<Record<string, unknown>>(query, params);
      return result.data.map((row) => this.transformToEntity(row));
    });
  }

  /**
   * Find records within a time range
   */
  async findByTimeRange(
    startTime: string | Date,
    endTime: string | Date,
    filters?: Record<string, unknown>,
    options?: TimeSeriesQueryOptions,
  ): Promise<T[]> {
    return this.executeWithErrorHandling('findByTimeRange', async () => {
      this.logOperation('findByTimeRange', { startTime, endTime, filters, options });

      const schema = this.getTableSchema();
      const timestampColumn = schema.designatedTimestamp || 'timestamp';

      const whereConditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Add time range conditions
      whereConditions.push(`${timestampColumn} >= $${paramIndex}`);
      params.push(typeof startTime === 'string' ? startTime : this.dateTimeUtils.formatUtcDateTime(startTime));
      paramIndex++;

      whereConditions.push(`${timestampColumn} <= $${paramIndex}`);
      params.push(typeof endTime === 'string' ? endTime : this.dateTimeUtils.formatUtcDateTime(endTime));
      paramIndex++;

      // Add additional filters
      if (filters) {
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null) {
            whereConditions.push(`${key} = $${paramIndex}`);
            params.push(value);
            paramIndex++;
          }
        }
      }

      // Build ORDER BY clause
      let orderByClause = `ORDER BY ${timestampColumn} ASC`;
      if (options?.orderBy && options.orderBy.length > 0) {
        const orderByParts = options.orderBy.map((order) => `${order.field} ${order.direction}`);
        orderByClause = `ORDER BY ${orderByParts.join(', ')}`;
      }

      // Build LIMIT and OFFSET clauses
      let limitClause = '';
      if (options?.limit) {
        limitClause = `LIMIT $${paramIndex}`;
        params.push(options.limit);
        paramIndex++;
      }

      let offsetClause = '';
      if (options?.offset) {
        offsetClause = `OFFSET $${paramIndex}`;
        params.push(options.offset);
      }

      const query = `
        SELECT * FROM ${schema.tableName}
        WHERE ${whereConditions.join(' AND ')}
        ${orderByClause}
        ${limitClause}
        ${offsetClause}
      `.trim();

      const result = await this.executeQuery<Record<string, unknown>>(query, params);
      return result.data.map((row) => this.transformToEntity(row));
    });
  }

  /**
   * Aggregate data over time periods
   */
  async aggregateByTime(
    aggregateField: string,
    aggregateFunction: 'SUM' | 'AVG' | 'COUNT' | 'MIN' | 'MAX',
    timeInterval: string,
    filters?: Record<string, unknown>,
  ): Promise<Array<{ timestamp: string; value: number }>> {
    return this.executeWithErrorHandling('aggregateByTime', async () => {
      this.logOperation('aggregateByTime', { aggregateField, aggregateFunction, timeInterval, filters });

      const schema = this.getTableSchema();
      const timestampColumn = schema.designatedTimestamp || 'timestamp';

      const whereConditions: string[] = [];
      const params: unknown[] = [];
      let paramIndex = 1;

      // Build WHERE clause from filters
      if (filters) {
        for (const [key, value] of Object.entries(filters)) {
          if (value !== undefined && value !== null) {
            whereConditions.push(`${key} = $${paramIndex}`);
            params.push(value);
            paramIndex++;
          }
        }
      }

      const query = `
        SELECT
          sample_by(${timeInterval}, ${timestampColumn}) as timestamp,
          ${aggregateFunction}(${aggregateField}) as value
        FROM ${schema.tableName}
        ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''}
        SAMPLE BY ${timeInterval}
        ORDER BY timestamp
      `;

      const result = await this.executeQuery<{ timestamp: string; value: number }>(query, params);
      return result.data;
    });
  }

  // ==================== UPSERT OPERATIONS ====================

  /**
   * Upsert (insert or update) a single record
   */
  async upsert(data: CreateData, conflictFields: string[]): Promise<T> {
    return this.executeWithErrorHandling('upsert', async () => {
      this.logOperation('upsert', { data, conflictFields });

      const transformedData = this.transformCreateData(data);
      const schema = this.getTableSchema();

      // Build insert query with ON CONFLICT clause
      const columns = Object.keys(transformedData);
      const placeholders = columns.map((_, index) => `$${index + 1}`);
      const values = Object.values(transformedData);

      // Build UPDATE clause for conflict resolution
      const updateClauses = columns
        .filter((col) => !conflictFields.includes(col))
        .map((col) => `${col} = EXCLUDED.${col}`);

      const query = `
        INSERT INTO ${schema.tableName} (${columns.join(', ')})
        VALUES (${placeholders.join(', ')})
        ON CONFLICT (${conflictFields.join(', ')})
        DO UPDATE SET ${updateClauses.join(', ')}
        RETURNING *
      `;

      const result = await this.executeQuery<Record<string, unknown>>(query, values);

      if (result.rowCount === 0) {
        throw createQuestDBRepositoryError.invalidData('Upsert operation returned no rows');
      }

      this.logger.log(`Successfully upserted record in ${schema.tableName}`);
      return this.transformToEntity(result.data[0]);
    });
  }

  /**
   * Batch upsert multiple records
   */
  async batchUpsert(
    data: CreateData[],
    conflictFields: string[],
    options?: Partial<BatchInsertOptions>,
  ): Promise<BatchOperationResult> {
    return this.executeWithErrorHandling('batchUpsert', async () => {
      const batchSize = options?.batchSize || 1000;
      const schema = this.getTableSchema();

      this.logOperation('batchUpsert', {
        recordCount: data.length,
        batchSize,
        conflictFields,
        table: schema.tableName,
      });

      const startTime = this.dateTimeUtils.getTime();
      let totalProcessed = 0;
      let successCount = 0;
      let failureCount = 0;
      const errors: string[] = [];

      // Process data in batches
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);

        try {
          const transformedBatch = batch.map((item) => this.transformCreateData(item));

          if (transformedBatch.length > 0) {
            // Build batch upsert query
            const columns = Object.keys(transformedBatch[0]);
            const valueRows = transformedBatch.map((row, rowIndex) => {
              const rowPlaceholders = columns.map((_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`);
              return `(${rowPlaceholders.join(', ')})`;
            });

            const allValues = transformedBatch.flatMap((row) => Object.values(row));

            // Build UPDATE clause for conflict resolution
            const updateClauses = columns
              .filter((col) => !conflictFields.includes(col))
              .map((col) => `${col} = EXCLUDED.${col}`);

            const query = `
              INSERT INTO ${schema.tableName} (${columns.join(', ')})
              VALUES ${valueRows.join(', ')}
              ON CONFLICT (${conflictFields.join(', ')})
              DO UPDATE SET ${updateClauses.join(', ')}
            `;

            const result = await this.executeQuery(query, allValues);
            successCount += result.rowCount;
          }

          totalProcessed += batch.length;
        } catch (error) {
          failureCount += batch.length;
          const errorMessage = this.errorUtils.getErrorMessage(error);
          errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMessage}`);

          this.logger.warn(`Batch upsert failed for batch starting at index ${i}`, {
            error: errorMessage,
            batchSize: batch.length,
          });
        }
      }

      const processingTimeMs = this.dateTimeUtils.getTime() - startTime;

      this.logger.log(`Batch upsert completed`, {
        table: schema.tableName,
        totalProcessed,
        successCount,
        failureCount,
        processingTimeMs,
      });

      return {
        totalProcessed,
        successCount,
        failureCount,
        processingTimeMs,
        errors,
      };
    });
  }
}
