/**
 * Base QuestDB repository interfaces for PatternTrade API
 *
 * Provides core repository contracts for QuestDB time-series database operations.
 * These interfaces ensure consistent data access patterns across all QuestDB repositories
 * while supporting time-series specific operations and batch processing.
 */

/**
 * QuestDB query result interface
 */
export interface QuestDBQueryResult<T = Record<string, unknown>> {
  /** Query result data */
  data: T[];
  /** Number of rows returned */
  rowCount: number;
  /** UTC timestamp when query was executed */
  executedAt: string;
  /** Query execution time in milliseconds */
  executionTimeMs: number;
}

/**
 * Time-series insert options for QuestDB operations
 */
export interface TimeSeriesInsertOptions {
  /** Table name */
  table: string;
  /** Timestamp for the record */
  timestamp: string | Date;
  /** Data to insert */
  data: Record<string, unknown>;
  /** Optional designated timestamp column name */
  designatedTimestamp?: string;
}

/**
 * Batch insert options for high-volume operations
 */
export interface BatchInsertOptions {
  /** Table name */
  table: string;
  /** Array of data records to insert */
  data: Record<string, unknown>[];
  /** Batch size for processing */
  batchSize?: number;
  /** Whether to ignore duplicate key errors */
  ignoreDuplicates?: boolean;
  /** Designated timestamp column name */
  designatedTimestamp?: string;
}

/**
 * Batch operation result
 */
export interface BatchOperationResult {
  /** Total number of records processed */
  totalProcessed: number;
  /** Number of successful inserts */
  successCount: number;
  /** Number of failed inserts */
  failureCount: number;
  /** Processing time in milliseconds */
  processingTimeMs: number;
  /** Array of error messages for failed operations */
  errors: string[];
}

/**
 * Table creation options
 */
export interface TableCreationOptions {
  /** Table name */
  tableName: string;
  /** SQL for creating the table */
  createTableSQL: string;
  /** SQL statements for creating indexes */
  createIndexesSQL?: string[];
  /** Whether to drop table if it exists */
  dropIfExists?: boolean;
}

/**
 * Query filter options for time-series data
 */
export interface TimeSeriesQueryOptions {
  /** Start timestamp for filtering */
  startTime?: string | Date;
  /** End timestamp for filtering */
  endTime?: string | Date;
  /** Maximum number of results */
  limit?: number;
  /** Number of results to skip */
  offset?: number;
  /** Fields to order by */
  orderBy?: Array<{
    field: string;
    direction: 'ASC' | 'DESC';
  }>;
  /** Additional filter conditions */
  filters?: Record<string, unknown>;
}

/**
 * Core QuestDB repository interface providing standard database operations
 *
 * @template T - Entity type
 * @template CreateData - Data type for entity creation
 * @template UpdateData - Data type for entity updates
 * @template ID - ID type (number for serial IDs, string for UUIDs)
 */
export interface IBaseQuestDBRepository<T, CreateData, UpdateData, ID = number> {
  /**
   * Execute a raw SQL query
   * @param query - SQL query string
   * @param params - Query parameters
   * @returns Promise<QuestDBQueryResult<T>> - Query result
   */
  executeQuery<R = T>(query: string, params?: unknown[]): Promise<QuestDBQueryResult<R>>;

  /**
   * Insert a single record
   * @param data - Entity creation data
   * @returns Promise<T> - Created entity
   */
  insert(data: CreateData): Promise<T>;

  /**
   * Insert multiple records in batch
   * @param data - Array of entity creation data
   * @param options - Batch insert options
   * @returns Promise<BatchOperationResult> - Batch operation result
   */
  batchInsert(data: CreateData[], options?: Partial<BatchInsertOptions>): Promise<BatchOperationResult>;

  /**
   * Find records by filter criteria
   * @param filters - Filter criteria
   * @param options - Query options
   * @returns Promise<T[]> - Array of matching entities
   */
  findByFilters(filters: Record<string, unknown>, options?: TimeSeriesQueryOptions): Promise<T[]>;

  /**
   * Find a single record by ID
   * @param id - Entity ID
   * @returns Promise<T | null> - Found entity or null
   */
  findById(id: ID): Promise<T | null>;

  /**
   * Update records by filter criteria
   * @param filters - Filter criteria for records to update
   * @param updates - Update data
   * @returns Promise<number> - Number of updated records
   */
  updateByFilters(filters: Record<string, unknown>, updates: UpdateData): Promise<number>;

  /**
   * Delete records by filter criteria
   * @param filters - Filter criteria for records to delete
   * @returns Promise<number> - Number of deleted records
   */
  deleteByFilters(filters: Record<string, unknown>): Promise<number>;

  /**
   * Count records matching filter criteria
   * @param filters - Filter criteria
   * @returns Promise<number> - Count of matching records
   */
  count(filters?: Record<string, unknown>): Promise<number>;

  /**
   * Check if table exists
   * @returns Promise<boolean> - Whether table exists
   */
  tableExists(): Promise<boolean>;

  /**
   * Create table with indexes
   * @param options - Table creation options
   * @returns Promise<void>
   */
  createTable(options: TableCreationOptions): Promise<void>;

  /**
   * Drop table
   * @param tableName - Name of table to drop
   * @returns Promise<void>
   */
  dropTable(tableName?: string): Promise<void>;
}

/**
 * Time-series specific repository interface
 *
 * @template T - Entity type
 * @template CreateData - Data type for entity creation
 */
export interface ITimeSeriesRepository<T, CreateData> {
  /**
   * Insert time-series data with designated timestamp
   * @param options - Time-series insert options
   * @returns Promise<T> - Inserted entity
   */
  insertTimeSeries(options: TimeSeriesInsertOptions): Promise<T>;

  /**
   * Find latest records for each unique identifier
   * @param identifierField - Field to group by for latest records
   * @param filters - Additional filter criteria
   * @param limit - Maximum number of results
   * @returns Promise<T[]> - Array of latest entities
   */
  findLatestByIdentifier(identifierField: string, filters?: Record<string, unknown>, limit?: number): Promise<T[]>;

  /**
   * Find records within a time range
   * @param startTime - Start timestamp
   * @param endTime - End timestamp
   * @param filters - Additional filter criteria
   * @param options - Query options
   * @returns Promise<T[]> - Array of entities within time range
   */
  findByTimeRange(
    startTime: string | Date,
    endTime: string | Date,
    filters?: Record<string, unknown>,
    options?: TimeSeriesQueryOptions,
  ): Promise<T[]>;

  /**
   * Aggregate data over time periods
   * @param aggregateField - Field to aggregate
   * @param aggregateFunction - Aggregation function (SUM, AVG, COUNT, etc.)
   * @param timeInterval - Time interval for grouping
   * @param filters - Filter criteria
   * @returns Promise<Array<{ timestamp: string; value: number }>> - Aggregated results
   */
  aggregateByTime(
    aggregateField: string,
    aggregateFunction: 'SUM' | 'AVG' | 'COUNT' | 'MIN' | 'MAX',
    timeInterval: string,
    filters?: Record<string, unknown>,
  ): Promise<Array<{ timestamp: string; value: number }>>;
}

/**
 * Repository interface for entities with upsert capabilities
 *
 * @template T - Entity type
 * @template UpsertData - Data type for upsert operations
 */
export interface IUpsertRepository<T, UpsertData> {
  /**
   * Upsert (insert or update) a single record
   * @param data - Upsert data
   * @param conflictFields - Fields to check for conflicts
   * @returns Promise<T> - Upserted entity
   */
  upsert(data: UpsertData, conflictFields: string[]): Promise<T>;

  /**
   * Batch upsert multiple records
   * @param data - Array of upsert data
   * @param conflictFields - Fields to check for conflicts
   * @param options - Batch operation options
   * @returns Promise<BatchOperationResult> - Batch operation result
   */
  batchUpsert(
    data: UpsertData[],
    conflictFields: string[],
    options?: Partial<BatchInsertOptions>,
  ): Promise<BatchOperationResult>;
}
