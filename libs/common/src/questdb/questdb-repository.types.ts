/**
 * QuestDB repository types and error definitions for PatternTrade API
 *
 * Provides QuestDB-specific error handling, operation context, and utility types
 * for consistent error management across all QuestDB repository implementations.
 */

import { z } from 'zod/v4';
import { BaseError } from '../errors';

/**
 * QuestDB repository error codes enum
 */
export const QuestDBRepositoryErrorCodeEnum = z.enum([
  // Connection and service errors
  'CONNECTION_FAILED',
  'CONNECTION_TIMEOUT',
  'CONNECTION_POOL_ERROR',
  'SERVICE_UNAVAILABLE',

  // Query execution errors
  'QUERY_EXECUTION_FAILED',
  'QUERY_TIMEOUT',
  'INVALID_QUERY_SYNTAX',
  'PARAMETER_BINDING_FAILED',

  // Table and schema errors
  'TABLE_NOT_FOUND',
  'TABLE_CREATION_FAILED',
  'INDEX_CREATION_FAILED',
  'SCHEMA_VALIDATION_FAILED',
  'COLUMN_NOT_FOUND',

  // Data operation errors
  'INSERT_FAILED',
  'UPDATE_FAILED',
  'DELETE_FAILED',
  'BATCH_OPERATION_FAILED',
  'UPSERT_FAILED',

  // Time-series specific errors
  'INVALID_TIMESTAMP_FORMAT',
  'TIMESTAMP_OUT_OF_RANGE',
  'DESIGNATED_TIMESTAMP_MISSING',
  'TIME_SERIES_INSERT_FAILED',

  // Data validation errors
  'INVALID_DATA_FORMAT',
  'MISSING_REQUIRED_FIELD',
  'DATA_TYPE_MISMATCH',
  'CONSTRAINT_VIOLATION',

  // Repository operation errors
  'ENTITY_NOT_FOUND',
  'DUPLICATE_ENTITY',
  'REPOSITORY_INITIALIZATION_FAILED',
  'OPERATION_NOT_SUPPORTED',

  // Performance and resource errors
  'MEMORY_LIMIT_EXCEEDED',
  'DISK_SPACE_INSUFFICIENT',
  'QUERY_COMPLEXITY_EXCEEDED',
  'RATE_LIMIT_EXCEEDED',
]);

export type QuestDBRepositoryErrorCode = z.output<typeof QuestDBRepositoryErrorCodeEnum>;

/**
 * Error messages mapping for QuestDB repository errors
 */
export const QuestDBRepositoryErrorMessages: Record<QuestDBRepositoryErrorCode, string> = {
  // Connection and service errors
  CONNECTION_FAILED: 'Failed to establish connection to QuestDB',
  CONNECTION_TIMEOUT: 'Connection to QuestDB timed out',
  CONNECTION_POOL_ERROR: 'QuestDB connection pool error',
  SERVICE_UNAVAILABLE: 'QuestDB service is currently unavailable',

  // Query execution errors
  QUERY_EXECUTION_FAILED: 'Query execution failed',
  QUERY_TIMEOUT: 'Query execution timed out',
  INVALID_QUERY_SYNTAX: 'Invalid SQL query syntax',
  PARAMETER_BINDING_FAILED: 'Failed to bind query parameters',

  // Table and schema errors
  TABLE_NOT_FOUND: 'Table does not exist',
  TABLE_CREATION_FAILED: 'Failed to create table',
  INDEX_CREATION_FAILED: 'Failed to create table index',
  SCHEMA_VALIDATION_FAILED: 'Schema validation failed',
  COLUMN_NOT_FOUND: 'Column does not exist in table',

  // Data operation errors
  INSERT_FAILED: 'Insert operation failed',
  UPDATE_FAILED: 'Update operation failed',
  DELETE_FAILED: 'Delete operation failed',
  BATCH_OPERATION_FAILED: 'Batch operation failed',
  UPSERT_FAILED: 'Upsert operation failed',

  // Time-series specific errors
  INVALID_TIMESTAMP_FORMAT: 'Invalid timestamp format for time-series data',
  TIMESTAMP_OUT_OF_RANGE: 'Timestamp is outside valid range',
  DESIGNATED_TIMESTAMP_MISSING: 'Designated timestamp column is missing',
  TIME_SERIES_INSERT_FAILED: 'Time-series insert operation failed',

  // Data validation errors
  INVALID_DATA_FORMAT: 'Invalid data format',
  MISSING_REQUIRED_FIELD: 'Required field is missing',
  DATA_TYPE_MISMATCH: 'Data type does not match expected type',
  CONSTRAINT_VIOLATION: 'Data constraint violation',

  // Repository operation errors
  ENTITY_NOT_FOUND: 'Entity not found',
  DUPLICATE_ENTITY: 'Entity already exists',
  REPOSITORY_INITIALIZATION_FAILED: 'Repository initialization failed',
  OPERATION_NOT_SUPPORTED: 'Operation is not supported',

  // Performance and resource errors
  MEMORY_LIMIT_EXCEEDED: 'Memory limit exceeded during operation',
  DISK_SPACE_INSUFFICIENT: 'Insufficient disk space for operation',
  QUERY_COMPLEXITY_EXCEEDED: 'Query complexity limit exceeded',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded for QuestDB operations',
};

/**
 * QuestDB repository-specific error class
 * Extends BaseError with QuestDB repository domain-specific error handling
 */
export class QuestDBRepositoryError extends BaseError<QuestDBRepositoryErrorCode> {
  constructor(
    code: QuestDBRepositoryErrorCode,
    details?: {
      message?: string;
      cause?: unknown;
      context?: Record<string, unknown>;
    },
  ) {
    super({
      name: code,
      domain: 'QUESTDB_REPOSITORY' as const,
      message: details?.message || QuestDBRepositoryErrorMessages[code],
      cause: details?.cause,
    });

    // Add additional context if provided
    if (details?.context) {
      Object.assign(this, { context: details.context });
    }

    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this);
  }
}

/**
 * Entity not found error for QuestDB repositories
 */
export class QuestDBEntityNotFoundError extends QuestDBRepositoryError {
  constructor(entityName: string, identifier: string | number, additionalContext?: Record<string, unknown>) {
    super('ENTITY_NOT_FOUND', {
      message: `${entityName} with identifier '${identifier}' not found`,
      context: {
        entityName,
        identifier,
        ...additionalContext,
      },
    });
  }
}

/**
 * Duplicate entity error for QuestDB repositories
 */
export class QuestDBDuplicateEntityError extends QuestDBRepositoryError {
  constructor(entityName: string, field: string, value: string | number, additionalContext?: Record<string, unknown>) {
    super('DUPLICATE_ENTITY', {
      message: `${entityName} with ${field} '${value}' already exists`,
      context: {
        entityName,
        field,
        value,
        ...additionalContext,
      },
    });
  }
}

/**
 * Query execution error for QuestDB repositories
 */
export class QuestDBQueryExecutionError extends QuestDBRepositoryError {
  constructor(query: string, cause?: unknown, additionalContext?: Record<string, unknown>) {
    super('QUERY_EXECUTION_FAILED', {
      message: `Query execution failed: ${query.substring(0, 100)}${query.length > 100 ? '...' : ''}`,
      cause,
      context: {
        query: query.substring(0, 500), // Store more of the query for debugging
        queryLength: query.length,
        ...additionalContext,
      },
    });
  }
}

/**
 * Batch operation error for QuestDB repositories
 */
export class QuestDBBatchOperationError extends QuestDBRepositoryError {
  constructor(
    operation: string,
    totalRecords: number,
    failedRecords: number,
    errors: string[],
    additionalContext?: Record<string, unknown>,
  ) {
    super('BATCH_OPERATION_FAILED', {
      message: `Batch ${operation} failed: ${failedRecords}/${totalRecords} records failed`,
      context: {
        operation,
        totalRecords,
        failedRecords,
        successfulRecords: totalRecords - failedRecords,
        errors: errors.slice(0, 10), // Limit error array size
        ...additionalContext,
      },
    });
  }
}

/**
 * Time-series operation error for QuestDB repositories
 */
export class QuestDBTimeSeriesError extends QuestDBRepositoryError {
  constructor(
    operation: string,
    timestamp: string | Date,
    cause?: unknown,
    additionalContext?: Record<string, unknown>,
  ) {
    super('TIME_SERIES_INSERT_FAILED', {
      message: `Time-series ${operation} failed for timestamp: ${timestamp}`,
      cause,
      context: {
        operation,
        timestamp: timestamp.toString(),
        ...additionalContext,
      },
    });
  }
}

/**
 * Repository operation context for QuestDB operations
 */
export interface QuestDBRepositoryContext {
  /** Repository name for logging and debugging */
  repositoryName: string;
  /** Table name being operated on */
  tableName: string;
  /** Operation being performed */
  operation: string;
  /** Current user ID if available */
  userId?: string;
  /** Request ID for tracing */
  requestId?: string;
  /** Transaction ID if within a transaction */
  transactionId?: string;
  /** Additional context metadata */
  metadata?: Record<string, unknown>;
}

/**
 * QuestDB table schema definition
 */
export interface QuestDBTableSchema {
  /** Table name */
  tableName: string;
  /** SQL for creating the table */
  createTableSQL: string;
  /** SQL statements for creating indexes */
  createIndexesSQL: string[];
  /** Query templates for common operations */
  queries: Record<string, string>;
  /** Designated timestamp column name */
  designatedTimestamp?: string;
  /** Partition strategy */
  partitionBy?: 'NONE' | 'DAY' | 'MONTH' | 'YEAR';
}

/**
 * Helper functions for creating QuestDB repository errors
 */
export const createQuestDBRepositoryError = {
  connection: (cause?: unknown, customMessage?: string): QuestDBRepositoryError =>
    new QuestDBRepositoryError('CONNECTION_FAILED', { message: customMessage, cause }),

  query: (query: string, cause?: unknown): QuestDBQueryExecutionError => new QuestDBQueryExecutionError(query, cause),

  entityNotFound: (entityName: string, identifier: string | number): QuestDBEntityNotFoundError =>
    new QuestDBEntityNotFoundError(entityName, identifier),

  duplicateEntity: (entityName: string, field: string, value: string | number): QuestDBDuplicateEntityError =>
    new QuestDBDuplicateEntityError(entityName, field, value),

  batchOperation: (
    operation: string,
    totalRecords: number,
    failedRecords: number,
    errors: string[],
  ): QuestDBBatchOperationError => new QuestDBBatchOperationError(operation, totalRecords, failedRecords, errors),

  timeSeries: (operation: string, timestamp: string | Date, cause?: unknown): QuestDBTimeSeriesError =>
    new QuestDBTimeSeriesError(operation, timestamp, cause),

  tableNotFound: (tableName: string): QuestDBRepositoryError =>
    new QuestDBRepositoryError('TABLE_NOT_FOUND', {
      message: `Table '${tableName}' does not exist`,
      context: { tableName },
    }),

  invalidData: (message: string, data?: unknown): QuestDBRepositoryError =>
    new QuestDBRepositoryError('INVALID_DATA_FORMAT', {
      message,
      context: { data },
    }),
};
