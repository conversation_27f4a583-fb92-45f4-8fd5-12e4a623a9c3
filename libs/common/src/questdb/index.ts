/**
 * QuestDB Repository Module Exports
 *
 * Centralized exports for QuestDB repository functionality including:
 * - Base repository interfaces and implementations
 * - Error types and helper functions
 * - Type definitions and schemas
 * - Utility functions and constants
 */

// Base repository interfaces
export {
  IBaseQuestDBRepository,
  ITimeSeriesRepository,
  IUpsertRepository,
  QuestDBQueryResult,
  TimeSeriesInsertOptions,
  BatchInsertOptions,
  BatchOperationResult,
  TableCreationOptions,
  TimeSeriesQueryOptions,
} from './base-questdb-repository.interface';

// Base repository implementation
export { BaseQuestDBRepository } from './base.questdb-repository';

// Error types and utilities
export {
  QuestDBRepositoryErrorCodeEnum,
  QuestDBRepositoryErrorMessages,
  QuestDBRepositoryError,
  QuestDBEntityNotFoundError,
  QuestDBDuplicateEntityError,
  QuestDBQueryExecutionError,
  QuestDBBatchOperationError,
  QuestDBTimeSeriesError,
  QuestDBRepositoryContext,
  QuestDBTableSchema,
  createQuestDBRepositoryError,
} from './questdb-repository.types';

// Type exports
export type {
  QuestDBRepositoryErrorCode,
} from './questdb-repository.types';
