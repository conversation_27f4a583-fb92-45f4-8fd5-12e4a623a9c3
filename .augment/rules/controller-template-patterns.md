---
type: "always_apply"
---

# PatternTrade API Controller Rules

**MANDATORY**: All controllers must follow these patterns exactly.

## Controller Structure

```typescript
@Controller('entities')
@UseGuards(AuthGuard, RolesGuard)
export class EntityController {
  private readonly logger = new Logger(EntityController.name);

  constructor(private readonly entityService: EntityService) {}
}
```

## DTO Creation (REQUIRED)

```typescript
class CreateEntityDto extends createZodDto(CreateEntitySchema) {}
class UpdateEntityDto extends createZodDto(UpdateEntitySchema) {}
class EntitySearchDto extends createZodDto(EntitySearchSchema) {}
```

## Route Patterns (REQUIRED)

### CRUD Operations
```typescript
@Post()
@Roles('admin')
async createEntity(@Body() dto: CreateEntityDto): Promise<Entity> {}

@Get()
@Roles('admin')
async findAllEntities(
  @Query() filters: EntitySearchDto,
  @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
): Promise<PaginatedResult<Entity>> {}

@Get(':id')
@Roles('admin', 'user')
async findEntityById(@Param('id', ParseIntPipe) id: number): Promise<Entity> {}

@Patch(':id')
@Roles('admin')
async updateEntity(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateEntityDto): Promise<Entity> {}

@Delete(':id')
@Roles('admin')
async deleteEntity(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {}
```

### Public Endpoints
```typescript
@Get('public')
@Public()
async getPublicData(): Promise<Data> {}
```

## Authentication Rules (REQUIRED)

- **Controller Level**: `@UseGuards(AuthGuard, RolesGuard)` on all controllers
- **Admin Only**: `@Roles('admin')` for management operations
- **User Access**: `@Roles('admin', 'user')` for general operations
- **Public**: `@Public()` for no authentication required

## Pagination Pattern (REQUIRED)

```typescript
const paginationOptions: PaginationOptions = {
  limit: limit || 10,
  offset: offset || 0,
  sortBy: sortBy || 'createdAt',
  sortOrder: sortOrder || 'DESC',
  filters: searchFilters as Record<string, unknown>,
};
```

## Logging Rules (REQUIRED)

```typescript
// Operation start
this.logger.log(`Creating entity with data: ${dto.name}`);

// Success
this.logger.log(`Successfully created entity with ID: ${entity.id}`);

// Not found (warning)
this.logger.warn(`Entity with ID ${id} not found`);

// Errors handled by exception filters - no manual logging needed
```

## Error Handling (REQUIRED)

```typescript
// Standard pattern - let exception filters handle domain errors
const entity = await this.entityService.findEntityById(id);
if (!entity) {
  throw new NotFoundException(`Entity with ID ${id} not found`);
}
```

## Response Patterns (REQUIRED)

```typescript
// Entity response
return entity;

// Success message
return { message: `Entity with ID ${id} has been deleted` };

// Paginated response
return result; // PaginatedResult<Entity>
```

## JSDoc Requirements (REQUIRED)

```typescript
/**
 * Create a new entity
 * Requirements: [spec reference]
 */
@Post()
async createEntity(@Body() dto: CreateEntityDto): Promise<Entity> {}
```

## Validation Rules (REQUIRED)

- **Path params**: `@Param('id', ParseIntPipe)` for IDs
- **Query params**: `new ParseIntPipe({ optional: true })` for optional
- **Body**: Use Zod DTOs - validation is automatic
- **Never use manual validation** - rely on nestjs-zod integration

## Naming Conventions (REQUIRED)

- **Controllers**: `{Entity}Controller`
- **DTOs**: `Create{Entity}Dto`, `Update{Entity}Dto`, `{Entity}SearchDto`
- **Methods**: `create{Entity}`, `findAll{Entities}`, `find{Entity}ById`, `update{Entity}`, `delete{Entity}`

## Dependencies (REQUIRED)

```typescript
constructor(
  private readonly entityService: EntityService,
  // Add utils only if needed for custom error handling
) {}
```

**CRITICAL**: Follow these patterns exactly. No exceptions without architectural review.
